#!/usr/bin/env python3
"""
Clean Graphiti Search Engine
============================

A production-ready search engine with clear separation of concerns:
- Graphiti LLM: Uses GRAPHITI_OLLAMA_MODEL for Graphiti Core operations
- Response LLM: Uses OLLAMA_MODEL for final answer generation  
- Embedder: Uses OpenAI API for vector embeddings

This engine provides a clean API for GUI integration while maintaining
robust error handling and performance optimization.
"""

import os
import sys
import time
import asyncio
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

import requests
from graphiti_core import Graphiti
from graphiti_core.search.search_config_recipes import (
    COMBINED_HYBRID_SEARCH_MMR,
    COMBINED_HYBRID_SEARCH_RRF,
    COMBINED_HYBRID_SEARCH_CROSS_ENCODER,
    NODE_HYBRID_SEARCH_RRF,
)
from graphiti_core.embedder.openai import OpenAIEmbedder, OpenAIEmbedderConfig
from graphiti_core.llm_client.config import LLMConfig
from graphiti_core.llm_client.openai_client import OpenAIClient

# Import utilities
from domain_ontology import enhance_query_with_domain_knowledge
from utils.url_normalizer import normalize_ollama_url

# Import ResponseBuilder for v2.0 responses
import importlib.util
import os
response_builder_path = os.path.join(os.path.dirname(__file__), "360t-kg-api/services/response-builder.py")
spec = importlib.util.spec_from_file_location("response_builder", response_builder_path)
response_builder_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(response_builder_module)
ResponseBuilder = response_builder_module.ResponseBuilder


@dataclass
class LLMConfiguration:
    """Configuration for the three LLM components"""
    # Graphiti internal operations (knowledge graph reasoning)
    graphiti_model: str
    graphiti_url: str
    
    # Final response generation (user-facing answers)
    response_model: str
    response_url: str
    
    # Vector embeddings
    embedding_model: str
    embedding_dimensions: int
    openai_api_key: str
    
    # Database connection
    neo4j_uri: str
    neo4j_user: str
    neo4j_password: str
    neo4j_database: str
    
    @classmethod
    def from_environment(cls) -> 'LLMConfiguration':
        """Create configuration from environment variables"""
        return cls(
            # Graphiti LLM (for internal operations)
            graphiti_model=os.getenv("GRAPHITI_OLLAMA_MODEL", "gemma3:latest"),
            graphiti_url=os.getenv("OLLAMA_URL", "http://localhost:11434"),
            
            # Response LLM (for final answers)
            response_model=os.getenv("OLLAMA_MODEL", "qwen3:30b-a3b-q8_0"),
            response_url=os.getenv("OLLAMA_URL", "http://localhost:11434"),
            
            # Embeddings
            embedding_model=os.getenv("OPENAI_EMBEDDING_MODEL", "text-embedding-3-small"),
            embedding_dimensions=int(os.getenv("OPENAI_EMBEDDING_DIMENSIONS", "1024")),
            openai_api_key=os.getenv("OPENAI_API_KEY", ""),
            
            # Database
            neo4j_uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
            neo4j_user=os.getenv("NEO4J_USER", "neo4j"),
            neo4j_password=os.getenv("NEO4J_PASSWORD", "password"),
            neo4j_database=os.getenv("NEO4J_DATABASE", "neo4j"),
        )
    
    @classmethod
    def from_graphiti_settings(cls, settings: Dict[str, Any]) -> 'LLMConfiguration':
        """Create configuration from frontend GraphitiSettings"""
        # Use environment defaults first, then override with frontend settings
        base_config = cls.from_environment()
        
        # Default to environment settings
        graphiti_model = base_config.graphiti_model
        graphiti_url = base_config.graphiti_url
        response_model = base_config.response_model
        response_url = base_config.response_url
        
        # Override with frontend settings if provided
        if settings and settings.get("llmProvider") == "ollama":
            # Use frontend Ollama settings for response generation
            response_url = settings.get("ollamaUrl", base_config.response_url)
            response_model = settings.get("ollamaModel", base_config.response_model)

            # Use frontend settings for Graphiti LLM - prioritize dedicated graphitiModel field
            if "graphitiModel" in settings:
                graphiti_model = settings["graphitiModel"]
            else:
                # Keep environment default if no frontend graphitiModel specified
                graphiti_model = base_config.graphiti_model

            # Update Graphiti URL as well
            graphiti_url = settings.get("ollamaUrl", base_config.graphiti_url)
        
        return cls(
            # Graphiti LLM (use frontend settings if available)
            graphiti_model=graphiti_model,
            graphiti_url=graphiti_url,
            
            # Response LLM (use frontend settings if available)
            response_model=response_model,
            response_url=response_url,
            
            # Embeddings (keep environment defaults)
            embedding_model=base_config.embedding_model,
            embedding_dimensions=base_config.embedding_dimensions,
            openai_api_key=base_config.openai_api_key,
            
            # Database (keep environment defaults)
            neo4j_uri=base_config.neo4j_uri,
            neo4j_user=base_config.neo4j_user,
            neo4j_password=base_config.neo4j_password,
            neo4j_database=base_config.neo4j_database,
        )


@dataclass
class SearchParameters:
    """Parameters for search operations"""
    edge_count: int = 6
    node_count: int = 2
    search_type: str = "COMBINED_HYBRID_SEARCH_CROSS_ENCODER"  # Match frontend default
    diversity_factor: float = 0.3
    temperature: float = 0.3
    timeout: int = 120
    enable_query_enhancement: bool = True
    conversation_history: Optional[List[Dict[str, str]]] = None
    
    @classmethod
    def from_graphiti_settings(cls, settings: Dict[str, Any], 
                              conversation_history: Optional[List[Dict[str, str]]] = None) -> 'SearchParameters':
        """Create search parameters from frontend GraphitiSettings"""
        # Use frontend default values to match GraphitiConfigPanel
        return cls(
            edge_count=settings.get("edgeCount", 6),
            node_count=settings.get("nodeCount", 2),
            search_type=settings.get("searchType", "COMBINED_HYBRID_SEARCH_CROSS_ENCODER"),  # Match frontend default
            diversity_factor=settings.get("diversityFactor", 0.3),
            temperature=settings.get("temperature", 0.3),
            timeout=settings.get("timeout", 120),
            enable_query_enhancement=True,  # Always enabled
            conversation_history=conversation_history or []
        )


class GraphitiSearchEngine:
    """
    Clean, production-ready search engine with separated LLM components
    """
    
    def __init__(self, config: LLMConfiguration):
        self.config = config
        self.response_builder = ResponseBuilder()
        self._graphiti_instance = None
        self._cached_config_hash = None  # Track config changes
        
    def _build_graphiti_instance(self) -> Graphiti:
        """Build Graphiti instance with proper LLM separation"""
        # Create a hash of the current configuration to detect changes
        import hashlib
        config_str = f"{self.config.graphiti_model}:{self.config.graphiti_url}:{self.config.openai_api_key}:{self.config.neo4j_uri}"
        current_config_hash = hashlib.md5(config_str.encode()).hexdigest()

        # Return cached instance only if configuration hasn't changed
        if (self._graphiti_instance is not None and
            self._cached_config_hash == current_config_hash):
            return self._graphiti_instance

        # Configuration changed or no cached instance - create new one
        if self._graphiti_instance is not None:
            # Clean up old instance if it exists
            try:
                # Note: close() might be async, but we'll handle it synchronously here
                pass  # We'll implement proper cleanup later if needed
            except:
                pass

        self._cached_config_hash = current_config_hash
            
        # Convert neo4j:// to bolt:// for better compatibility
        neo4j_uri = self.config.neo4j_uri
        if neo4j_uri.startswith("neo4j://"):
            neo4j_uri = neo4j_uri.replace("neo4j://", "bolt://")
        
        # Configure Graphiti's internal LLM (for knowledge graph operations)
        # Use configuration from self.config instead of hardcoded values
        if self.config.openai_api_key and self.config.openai_api_key.startswith("sk-"):
            # Use OpenAI for Graphiti internal operations
            graphiti_llm_config = LLMConfig(
                api_key=self.config.openai_api_key,
                model="gpt-4o-mini",  # OpenAI model for Graphiti operations
                small_model="gpt-4o-mini",  # Use same model for small operations
            )
        else:
            # Use Ollama for Graphiti internal operations (default)
            graphiti_llm_config = LLMConfig(
                api_key="ollama",  # Ollama doesn't require real API key
                model=self.config.graphiti_model,
                small_model=self.config.graphiti_model,  # Use same model for both operations
                base_url=f"{self.config.graphiti_url}/v1",
            )

        llm_client = OpenAIClient(config=graphiti_llm_config)
        
        # Configure embedder (OpenAI for consistency with existing data)
        embedder_config = OpenAIEmbedderConfig(
            api_key=self.config.openai_api_key,
            embedding_model=self.config.embedding_model,
            embedding_dim=self.config.embedding_dimensions
        )
        embedder = OpenAIEmbedder(config=embedder_config)

    #     cross_encoder=OpenAIRerankerClient(config=llm_config)
    # )
        
        # Create Graphiti instance with positional arguments
        self._graphiti_instance = Graphiti(
            neo4j_uri,  # Positional argument
            self.config.neo4j_user,  # Positional argument  
            self.config.neo4j_password,  # Positional argument
            llm_client=llm_client,
            embedder=embedder,
            # cross_encoder=None  # Disabled due to compatibility issues
        )
        
        return self._graphiti_instance
    
    def _get_search_config(self, search_type: str, diversity_factor: float):
        """Get search configuration based on type"""
        if search_type == "COMBINED_HYBRID_SEARCH_MMR":
            cfg = COMBINED_HYBRID_SEARCH_MMR.model_copy(deep=True)
            # Set MMR lambda for diversity control
            mmr_lambda = 1.0 - diversity_factor
            for config_attr in ['community_config', 'edge_config', 'node_config']:
                if hasattr(cfg, config_attr):
                    config_obj = getattr(cfg, config_attr)
                    if config_obj and hasattr(config_obj, 'mmr_lambda'):
                        config_obj.mmr_lambda = mmr_lambda
            return cfg
        elif search_type == "COMBINED_HYBRID_SEARCH_CROSS_ENCODER":
            return COMBINED_HYBRID_SEARCH_CROSS_ENCODER.model_copy(deep=True)
        elif search_type == "NODE_HYBRID_SEARCH_RRF":
            return NODE_HYBRID_SEARCH_RRF.model_copy(deep=True)
        else:  # Default to RRF
            return COMBINED_HYBRID_SEARCH_RRF.model_copy(deep=True)
    
    async def _search_context(self, query: str, params: SearchParameters) -> tuple[str, List[Dict], List[Dict], Dict[str, Any]]:
        """
        Search for context using Graphiti
        Returns: (context_markdown, search_results, citations, search_metadata)
        """
        graphiti = self._build_graphiti_instance()
        
        # Enhance query with domain knowledge if enabled
        enhanced_query = query
        if params.enable_query_enhancement:
            enhancement_result = enhance_query_with_domain_knowledge(query)
            enhanced_query = enhancement_result.get("enhanced_query", query)
        
        # Get search configuration
        search_config = self._get_search_config(params.search_type, params.diversity_factor)
        
        # Calculate search limit with buffer for better filtering
        search_limit = max(10, (params.edge_count + params.node_count) * 3)
        search_config.limit = search_limit
        
        # Perform search using the internal _search method with config
        search_results = await graphiti._search(
            query=enhanced_query,
            config=search_config
        )
        
        # Process results into context
        context_items = []
        citations = []
        
        # Extract edges (relationship facts)
        if hasattr(search_results, 'edges') and search_results.edges:
            for i, edge in enumerate(search_results.edges[:params.edge_count]):
                fact = edge.fact if hasattr(edge, 'fact') else str(edge)
                context_items.append(f"[{i+1}] {fact}")
                citations.append({
                    'index': i+1,
                    'content': fact,
                    'type': 'edge',
                    'source': edge
                })
        
        # Extract nodes (entity summaries)
        if hasattr(search_results, 'nodes') and search_results.nodes:
            for i, node in enumerate(search_results.nodes[:params.node_count]):
                if hasattr(node, 'summary') and node.summary:
                    context_items.append(f"[{len(context_items)+1}] {node.summary}")
                    citations.append({
                        'index': len(context_items),
                        'content': node.summary,
                        'type': 'node',
                        'source': node
                    })
        
        context_markdown = "\n\n".join(context_items)
        
        # Build search metadata
        search_metadata = {
            "algorithm": params.search_type,
            "diversity_factor": params.diversity_factor,
            "requested": {
                "edges": params.edge_count,
                "nodes": params.node_count
            },
            "returned": {
                "edges": len([c for c in citations if c['type'] == 'edge']),
                "nodes": len([c for c in citations if c['type'] == 'node']),
                "total_results": len(citations)
            },
            "search_limit": search_limit,
            "enhanced_query": enhanced_query if enhanced_query != query else None
        }
        
        return context_markdown, search_results, citations, search_metadata
    
    async def _generate_response(self, query: str, context: str, params: SearchParameters) -> tuple[str, Dict[str, Any]]:
        """
        Generate final response using the dedicated response LLM
        Returns: (response_text, llm_metadata)
        """
        # Build conversation context
        conversation_context = ""
        if params.conversation_history:
            recent_history = params.conversation_history[-6:]  # Last 6 messages
            history_items = []
            for msg in recent_history:
                role = msg.get('role', 'user')
                content = msg.get('content', '')[:200]  # Truncate for brevity
                history_items.append(f"{role.title()}: {content}")
            if history_items:
                conversation_context = f"=== CONVERSATION CONTEXT ===\n" + "\n".join(history_items) + "\n\n"
        
        # Build prompt
        prompt = f"""
You are a FOREX trading platform expert with access to a structured knowledge graph built from 360T product user guides (via Graphiti). Use the provided knowledge graph context as your only information source. Do not add any information that is not in the context, and do not guess or hallucinate.

{conversation_context}

=== KNOWLEDGE GRAPH CONTEXT ===
{context}

=== QUESTION ===
{query}

## Answer (concise)
Provide a clear, direct answer in 3–4 sentences that addresses the question using only the information above.

## Explanation
Give a more detailed explanation of your answer. Reference specific facts from the knowledge graph context to support your answer (for example, cite as [1], [2], etc. that correspond to items in the context).

## Follow Up Questions
- List 3 relevant, open-ended follow-up questions (as bullet points) that the user could ask next, based on the topic and the provided context.
"""
        
        
        
        # Call response LLM
        api_url = normalize_ollama_url(self.config.response_url)

        payload = {
            "model": self.config.response_model,
            "prompt": prompt,
            "options": {
                "temperature": params.temperature,
                "top_p": 0.9,
                "top_k": 40,
            },
            "stream": False,
        }
        
        # Track generation timing and metadata
        generation_start = time.time()
        
        try:
            response = requests.post(
                api_url,  # URL is already normalized to include /api/generate
                json=payload,
                timeout=params.timeout
            )
            response.raise_for_status()
            data = response.json()
            
            generation_time = time.time() - generation_start
            response_text = data.get("response", "")
            
            # Build LLM metadata
            llm_metadata = {
                "model_used": self.config.response_model,
                "api_url": api_url,
                "request_parameters": {
                    "temperature": params.temperature,
                    "top_p": 0.9,
                    "top_k": 40,
                },
                "response_metrics": {
                    "generation_time_ms": int(generation_time * 1000),
                    "response_length_chars": len(response_text),
                    "response_length_words": len(response_text.split()) if response_text else 0,
                    "prompt_length_chars": len(prompt)
                }
            }
            
            # Add Ollama-specific metadata if available
            if 'eval_count' in data:
                llm_metadata["response_metrics"]["tokens_generated"] = data.get('eval_count')
            if 'prompt_eval_count' in data:
                llm_metadata["response_metrics"]["prompt_tokens"] = data.get('prompt_eval_count')
            if 'total_duration' in data:
                llm_metadata["response_metrics"]["total_duration_ns"] = data.get('total_duration')
                
            return response_text, llm_metadata
            
        except Exception as e:
            error_metadata = {
                "model_used": self.config.response_model,
                "api_url": api_url,
                "error": str(e),
                "generation_time_ms": int((time.time() - generation_start) * 1000)
            }
            return f"❌ Response generation failed: {str(e)}", error_metadata
    
    async def search_and_respond(self, query: str, params: SearchParameters = None) -> Dict[str, Any]:
        """
        Main entry point: search for context and generate structured response
        """
        if params is None:
            params = SearchParameters()
        
        start_time = time.time()
        
        try:
            # Phase 1: Search for context
            search_start = time.time()
            context_markdown, search_results, citations, search_metadata = await self._search_context(query, params)
            search_time = time.time() - search_start
            
            if not context_markdown:
                return {
                    "success": False,
                    "error": "No relevant context found",
                    "version": "2.0"
                }
            
            # Phase 2: Generate response
            response_start = time.time()
            answer, llm_metadata = await self._generate_response(query, context_markdown, params)
            response_time = time.time() - response_start
            
            # Phase 3: Build structured response
            build_start = time.time()
            # Combine all metadata
            enhanced_metadata = {
                # Legacy metadata for compatibility
                "search_type": params.search_type,
                "search_time_ms": int(search_time * 1000),
                "response_time_ms": int(response_time * 1000),
                "total_time_ms": int((time.time() - start_time) * 1000),
                "graphiti_model": self.config.graphiti_model,
                "response_model": self.config.response_model,
                "embedding_model": self.config.embedding_model,
                
                # Enhanced search metadata
                "graphiti_search": search_metadata,
                
                # Enhanced LLM metadata
                "llm_models": {
                    "graphiti_llm": {
                        "model": self.config.graphiti_model,
                        "url": self.config.graphiti_url,
                        "purpose": "Knowledge graph search operations"
                    },
                    "response_llm": {
                        **llm_metadata,
                        "purpose": "Final answer generation"
                    }
                }
            }
            
            structured_response = self.response_builder.build_structured_response(
                answer=answer,
                search_results=search_results,
                citations=citations,
                question=query,
                search_metadata=enhanced_metadata
            )
            build_time = time.time() - build_start
            
            # Add timing information
            total_time = time.time() - start_time
            structured_response["timing"] = {
                "total": total_time,
                "search": search_time,
                "response": response_time,
                "build": build_time
            }
            
            return structured_response
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "version": "2.0",
                "timing": {
                    "total": time.time() - start_time,
                    "search": 0,
                    "response": 0,
                    "build": 0
                }
            }
    
    async def close(self):
        """Clean up resources"""
        if self._graphiti_instance:
            await self._graphiti_instance.close()
            self._graphiti_instance = None


# Factory function for easy instantiation
def create_search_engine(graphiti_settings: Optional[Dict[str, Any]] = None) -> GraphitiSearchEngine:
    """Create a search engine with environment or frontend configuration"""
    if graphiti_settings:
        config = LLMConfiguration.from_graphiti_settings(graphiti_settings)
    else:
        config = LLMConfiguration.from_environment()
    return GraphitiSearchEngine(config)


# CLI interface for testing
async def main():
    """CLI interface for testing the search engine"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Clean Graphiti Search Engine")
    parser.add_argument("query", help="Search query")
    parser.add_argument("--edges", type=int, default=6, help="Number of edges to retrieve")
    parser.add_argument("--nodes", type=int, default=2, help="Number of nodes to retrieve")
    parser.add_argument("--search-type", default="COMBINED_HYBRID_SEARCH_CROSS_ENCODER", 
                       help="Search algorithm type")
    parser.add_argument("--diversity", type=float, default=0.3, 
                       help="Diversity factor for MMR")
    parser.add_argument("--temperature", type=float, default=0.3,
                       help="LLM temperature")
    
    args = parser.parse_args()
    
    # Create search engine
    engine = create_search_engine()
    
    # Create search parameters
    params = SearchParameters(
        edge_count=args.edges,
        node_count=args.nodes,
        search_type=args.search_type,
        diversity_factor=args.diversity,
        temperature=args.temperature
    )
    
    try:
        # Perform search
        print(f"🔍 Searching for: {args.query}")
        print(f"⚙️  Configuration:")
        print(f"   - Graphiti LLM: {engine.config.graphiti_model}")
        print(f"   - Response LLM: {engine.config.response_model}")
        print(f"   - Embeddings: {engine.config.embedding_model}")
        print(f"   - Search type: {args.search_type}")
        print()
        
        result = await engine.search_and_respond(args.query, params)
        
        if result.get("success", True):
            print("✅ Search completed successfully")
            print(f"⏱️  Total time: {result['timing']['total']:.2f}s")
            print(f"   - Search: {result['timing']['search']:.2f}s")
            print(f"   - Response: {result['timing']['response']:.2f}s")
            print(f"   - Build: {result['timing']['build']:.2f}s")
            print()
            print("📋 Response:")
            print(result.get("answer", "No answer generated"))
        else:
            print(f"❌ Search failed: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await engine.close()


if __name__ == "__main__":
    asyncio.run(main())