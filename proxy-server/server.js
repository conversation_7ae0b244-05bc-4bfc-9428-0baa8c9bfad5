/**
 * Knowledge Graph QA Proxy Server
 * 
 * Express.js proxy server that acts as a middleman between the frontend
 * and the Python FastAPI service, handling session management and API routing.
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const session = require('express-session');
const rateLimit = require('express-rate-limit');

const winston = require('winston');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config();

// Initialize Express app
const app = express();

// Environment configuration
const NODE_ENV = process.env.NODE_ENV || 'development';
const PORT = process.env.PROXY_PORT || 3003;
const FASTAPI_URL = process.env.FASTAPI_URL || 'http://localhost:8000';
const BACKEND_URL = process.env.LEGACY_BASE_URL || 'http://localhost:3002';
const SESSION_SECRET = process.env.SESSION_SECRET || 'your-secret-key-change-in-production';

// Configure Winston logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'kg-qa-proxy' },
  transports: [
    new winston.transports.File({ filename: 'logs/proxy-error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/proxy-combined.log' }),
  ],
});

// Add console logging in development
if (NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }));
}

// CORS configuration MUST come FIRST to handle preflight requests
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    // Allow localhost in development
    if (NODE_ENV === 'development' && origin.includes('localhost')) {
      return callback(null, true);
    }

    // Add your frontend domains here
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:3002',
      'http://localhost:5173', // Vite dev server (default)
      'http://localhost:5177', // Original frontend port
      'http://localhost:5178', // ✅ FIXED - Actual current frontend port (Vite fallback)
    ];

    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  // Allow headers required by browser preflight for streaming fetch requests
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Cache-Control', 'Pragma', 'Accept'],
};

// Apply CORS middleware FIRST
app.use(cors(corsOptions));

// SENIOR ARCHITECT SOLUTION: Proxy routes AFTER CORS to ensure headers are applied
// Configure proxy middleware AFTER CORS but BEFORE body parsing middleware

// Backend API routes (routes to Backend API service for graph, analysis, etc.)
const { createFastAPIProxy, createBackendAPIProxy, createChatProxy, createConversationProxy } = require('./middleware/proxyMiddleware');
const backendProxy = createBackendAPIProxy(BACKEND_URL);

// Create specialized proxies using the correct functions
const fastAPIProxy = createConversationProxy(FASTAPI_URL);
console.log('🚨 DEBUG: Creating chat proxy...');
const chatProxy = createChatProxy(FASTAPI_URL);
console.log('🚨 DEBUG: Chat proxy created, registering route...');

// CRITICAL: Configure proxy routes AFTER CORS but BEFORE body parsing middleware
// IMPORTANT: More specific routes must come BEFORE general routes
// Route chat streaming message endpoints to Backend API (Node.js) for streaming functionality
app.use('/api/chat/message/stream', createBackendAPIProxy(BACKEND_URL, {
  pathRewrite: function(path, req) {
    // Keep the path as is since Node.js API expects /api/chat/message/stream
    logger.info(`Chat streaming route: ${req.originalUrl} -> ${req.originalUrl}`);
    return req.originalUrl;
  },
  timeout: 180000, // 3 minute timeout for streaming
}));
console.log('🚨 DEBUG: Chat streaming route registered at /api/chat/message/stream -> Node.js API');

// Route chat message endpoints to Backend API (Node.js) where the chat message processing lives
app.use('/api/chat/message', createBackendAPIProxy(BACKEND_URL, {
  pathRewrite: function(path, req) {
    // Keep the path as is since Node.js API expects /api/chat/message
    logger.info(`Chat message route: ${req.originalUrl} -> ${req.originalUrl}`);
    return req.originalUrl;
  },
  timeout: 150000, // Longer timeout for chat processing (2.5 minutes)
}));
console.log('🚨 DEBUG: Chat message route registered at /api/chat/message -> Node.js API');

// REMOVED: FastAPI streaming route - FastAPI doesn't have streaming endpoints implemented
// All streaming functionality is now handled by the Node.js API backend

// Route chat conversations to Backend API (Node.js) where the PostgreSQL data lives
app.use('/api/chat/conversations', createBackendAPIProxy(BACKEND_URL, {
  pathRewrite: function(path, req) {
    // Keep the path as is since Node.js API expects /api/chat/conversations
    logger.info(`Chat conversations route: ${req.originalUrl} -> ${req.originalUrl}`);
    return req.originalUrl;
  },
  timeout: 30000, // Shorter timeout for conversation CRUD operations
}));
console.log('🚨 DEBUG: Chat conversations route registered at /api/chat/conversations -> Node.js API');

// FIXED: Route conversations to Backend API where the real data and CRUD operations exist
app.use('/api/conversations', createBackendAPIProxy(BACKEND_URL, {
  pathRewrite: function(path, req) {
    // Convert /api/conversations/* to /api/chat/conversations/*
    const newPath = req.originalUrl.replace('/api/conversations', '/api/chat/conversations');
    logger.info(`Conversation path rewrite: ${req.originalUrl} -> ${newPath}`);
    return newPath;
  },
  timeout: 30000, // Shorter timeout for conversation CRUD operations
}));
app.use('/api/graph', backendProxy);
app.use('/api/analysis', backendProxy);
app.use('/api/settings', backendProxy);
app.use('/api/metadata', backendProxy);
app.use('/api/ollama', backendProxy);
app.use('/api/docs', backendProxy); // Add documentation proxy route

// NOW configure ALL other middleware AFTER proxy routes
// Middleware setup
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// CORS configuration moved to top of file before proxy routes

// Body parsing for non-proxy routes
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Session configuration
app.use(session({
  secret: SESSION_SECRET,
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: NODE_ENV === 'production', // Use secure cookies in production
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
  },
  name: 'kg-qa-session',
}));

// Rate limiting - Development-friendly configuration
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: NODE_ENV === 'development' ? 10000 : 100, // Much higher limit for development
  message: {
    error: 'Too many requests from this IP, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Skip rate limiting for health checks and large payload endpoints
  skip: (req) => {
    if (NODE_ENV === 'development') {
      // Skip rate limiting for long-running operations in development
      return req.url.includes('/api/graph/expand') ||
             req.url.includes('/api/health') ||
             req.url.includes('/health') ||
             req.url.includes('/api/chat') ||
             req.url.includes('/chat');
    }
    return false;
  },
});

// Apply rate limiting conditionally
if (NODE_ENV !== 'test') {
  app.use(limiter);
}

// Request logging middleware
app.use((req, res, next) => {
  console.log(`🚨 DEBUG: Request received: ${req.method} ${req.url}`);
  logger.info(`${req.method} ${req.url}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    sessionId: req.sessionID,
  });
  next();
});

// Import health check middleware
const { healthCheckHandler, startPeriodicHealthChecks } = require('./middleware/healthCheck');

// Basic health endpoint - independent of upstream services for startup
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'Proxy Server',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    uptime: process.uptime()
  });
});

// Comprehensive health endpoint that checks upstream services
app.get('/health/full', healthCheckHandler);

// Enhanced readiness endpoints for dev orchestrator
app.get('/api/ready', async (req, res) => {
  const readinessStatus = {
    status: 'ready',
    service: 'Proxy Server',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    checks: {}
  };
  
  let overallReady = true;
  
  try {
    // Check FastAPI connectivity
    try {
      const startTime = Date.now();
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);
      
      const fastApiResponse = await fetch(`${FASTAPI_URL}/health`, {
        signal: controller.signal,
        method: 'GET',
        headers: { 'User-Agent': 'proxy-readiness-check/1.0' }
      });
      
      clearTimeout(timeoutId);
      
      readinessStatus.checks.fastapi = {
        status: fastApiResponse.ok ? 'ok' : 'error',
        responseTime: `${Date.now() - startTime}ms`,
        url: `${FASTAPI_URL}/health`
      };
      
      if (!fastApiResponse.ok) {
        overallReady = false;
      }
    } catch (fastApiError) {
      readinessStatus.checks.fastapi = {
        status: 'error',
        error: fastApiError.message,
        url: `${FASTAPI_URL}/health`
      };
      overallReady = false;
    }
    
    // Check Backend API connectivity
    try {
      const startTime = Date.now();
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);
      
      const backendResponse = await fetch(`${BACKEND_URL}/api/health`, {
        signal: controller.signal,
        method: 'GET',
        headers: { 'User-Agent': 'proxy-readiness-check/1.0' }
      });
      
      clearTimeout(timeoutId);
      
      readinessStatus.checks.backend = {
        status: backendResponse.ok ? 'ok' : 'error',
        responseTime: `${Date.now() - startTime}ms`,
        url: `${BACKEND_URL}/api/health`
      };
      
      if (!backendResponse.ok) {
        overallReady = false;
      }
    } catch (backendError) {
      readinessStatus.checks.backend = {
        status: 'error',
        error: backendError.message,
        url: `${BACKEND_URL}/api/health`
      };
      overallReady = false;
    }
    
    // Check session store
    readinessStatus.checks.sessions = {
      status: 'ok',
      message: 'Memory session store operational'
    };
    
    // Check required environment variables
    const requiredEnvVars = ['FASTAPI_URL', 'SESSION_SECRET'];
    const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
    
    readinessStatus.checks.configuration = {
      status: missingEnvVars.length === 0 ? 'ok' : 'warn',
      missingEnvVars: missingEnvVars,
      message: missingEnvVars.length === 0 ? 'All required environment variables present' : `Missing: ${missingEnvVars.join(', ')}`
    };
    
    if (missingEnvVars.length > 0) {
      overallReady = false;
    }
    
  } catch (error) {
    readinessStatus.checks.general = {
      status: 'error',
      error: error.message
    };
    overallReady = false;
  }
  
  readinessStatus.status = overallReady ? 'ready' : 'not-ready';
  res.status(overallReady ? 200 : 503).json(readinessStatus);
});

// Upstream health check endpoint for dev orchestrator
app.get('/api/upstream-health', async (req, res) => {
  const upstreamStatus = {
    status: 'ok',
    service: 'Upstream Services',
    timestamp: new Date().toISOString(),
    upstreams: {}
  };
  
  let allUpstreamsHealthy = true;
  
  // Check all upstream services
  const upstreams = [
    { name: 'fastapi', url: `${FASTAPI_URL}/health` },
    { name: 'backend', url: `${BACKEND_URL}/api/health` }
  ];
  
  for (const upstream of upstreams) {
    try {
      const startTime = Date.now();
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);
      
      const response = await fetch(upstream.url, {
        signal: controller.signal,
        method: 'GET',
        headers: { 'User-Agent': 'proxy-upstream-check/1.0' }
      });
      
      clearTimeout(timeoutId);
      
      upstreamStatus.upstreams[upstream.name] = {
        status: response.ok ? 'healthy' : 'unhealthy',
        responseTime: `${Date.now() - startTime}ms`,
        httpStatus: response.status,
        url: upstream.url
      };
      
      if (!response.ok) {
        allUpstreamsHealthy = false;
      }
    } catch (error) {
      upstreamStatus.upstreams[upstream.name] = {
        status: 'error',
        error: error.message,
        url: upstream.url
      };
      allUpstreamsHealthy = false;
    }
  }
  
  upstreamStatus.status = allUpstreamsHealthy ? 'ok' : 'degraded';
  res.status(allUpstreamsHealthy ? 200 : 503).json(upstreamStatus);
});

// Start periodic health monitoring
startPeriodicHealthChecks();



// Import route modules
const { addRequestTiming } = require('./middleware/proxyMiddleware');

// Add request timing middleware
app.use(addRequestTiming);

// Proxy routes already configured above before body parsing
// Health check endpoint for backend API
app.use('/api/health', backendProxy);

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error('Unhandled error:', err);
  
  // Don't leak error details in production
  const errorMessage = NODE_ENV === 'production' 
    ? 'Internal server error'
    : err.message;
  
  res.status(err.status || 500).json({
    error: errorMessage,
    timestamp: new Date().toISOString(),
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Not found',
    message: `Route ${req.method} ${req.url} not found`,
  });
});

// Start server only if not in test environment
let server;
if (process.env.NODE_ENV !== 'test') {
  server = app.listen(PORT, 'localhost', () => {
    logger.info(`Proxy server running on port ${PORT}`);
    logger.info(`Proxying to FastAPI at: ${FASTAPI_URL}`);
    logger.info(`Environment: ${NODE_ENV}`);
  });
}

// Export app for testing
module.exports = app;

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});

module.exports = app;
