const express = require('express');
const { spawn } = require('child_process');
const path = require('path');
const { generateTitleFromMessage, isValidForTitle } = require('../utils/titleGenerator');
const router = express.Router();

// PostgreSQL client
const { Pool } = require('pg');
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'kg_qa_db',
  password: process.env.DB_PASSWORD || '',
  port: process.env.DB_PORT || 5432,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

module.exports = function(driver) {
  // PostgreSQL-based chat storage layer
  
  // Helper to generate a unique ID compatible with PostgreSQL UUID type
  const generateId = () => require('crypto').randomUUID();

  // POST /api/chat/message
  // Receives a message from the user, processes it, and returns a response.
  router.post('/message', async (req, res, next) => {
    // Debug logging - uncomment for development
    // console.log('Received POST /api/chat/message');
    // console.log('Request body:', JSON.stringify(req.body, null, 2));

    const { message, history, graphitiSettings } = req.body;

    // Validate required fields
    if (!message) {
      console.log('Error: Message is required');
      return res.status(400).json({ error: 'Message is required' });
    }
    
    // Validate message is a string
    if (typeof message !== 'string') {
      console.log('Error: Message must be a string');
      return res.status(400).json({ error: 'Message must be a string' });
    }
    
    // Validate message is not empty after trimming
    if (message.trim().length === 0) {
      console.log('Error: Message cannot be empty');
      return res.status(400).json({ error: 'Message cannot be empty' });
    }
    
    // Validate history if provided
    if (history && !Array.isArray(history)) {
      console.log('Error: History must be an array');
      return res.status(400).json({ error: 'History must be an array' });
    }

    try {
      // Call the Python QA pipeline directly
      const result = await callPythonQAPipeline(message, history, graphitiSettings);

      // Expect only v2.0 structured responses from Python script
      if (!result.structured_response || !result.structured_response.version || result.structured_response.version !== "2.0") {
        throw new Error('Python script must return v2.0 structured response format');
      }
      
      const v2Response = result.structured_response;
      // Debug logging - uncomment for development
      // console.log('✅ Using v2.0 structured response from Python script');

      // Generate smart title from user message
      const smartTitle = isValidForTitle(message) 
        ? generateTitleFromMessage(message)
        : 'New Conversation';

      // Save the conversation to PostgreSQL (let database auto-generate INTEGER ID)
      const newConversation = {
        title: smartTitle,
        description: `Chat about: ${message.substring(0, 100)}...`,
        is_active: true,
        is_archived: false,
        metadata: {
          graphitiSettings: graphitiSettings || {},
          source: 'chat-api',
          original_message: message // Store original message for reference
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        history: [
          { role: 'user', content: message, timestamp: new Date().toISOString() },
          { role: 'assistant', content: JSON.stringify(v2Response), timestamp: new Date().toISOString() }
        ]
      };

      // Insert conversation into PostgreSQL using proper UUID format
      const insertQuery = `
        INSERT INTO chat_sessions (user_id, title, description, is_active, is_archived, metadata, created_at, updated_at)
        VALUES ($1::uuid, $2, $3, $4, $5, $6, $7, $8)
        RETURNING *;
      `;
      
      const insertedConversation = await pool.query(insertQuery, [
        'ec540e89-4d5f-406e-8227-5a571147e7da', // Default admin user UUID from database initialization
        newConversation.title,
        newConversation.description,
        newConversation.is_active,
        newConversation.is_archived,
        JSON.stringify(newConversation.metadata),
        newConversation.created_at,
        newConversation.updated_at
      ]);

      // Get the inserted conversation ID
      const conversationId = insertedConversation.rows[0].id;

      // Insert messages into PostgreSQL with proper UUID format
      for (const msg of newConversation.history) {
        const messageQuery = `
          INSERT INTO messages (session_id, user_id, role, content, metadata, created_at)
          VALUES ($1::uuid, $2::uuid, $3, $4, $5, $6);
        `;
        
        await pool.query(messageQuery, [
          conversationId,
          'ec540e89-4d5f-406e-8227-5a571147e7da', // Default admin user UUID
          msg.role,
          msg.content,
          JSON.stringify({ source: 'chat-api' }),
          msg.timestamp
        ]);
      }

      res.json({
        response: {
          role: 'assistant',
          content: JSON.stringify(v2Response),
          timestamp: new Date().toISOString(),
          sourceDocuments: result.source_documents || [],
          sourceNodes: result.source_nodes || []
        },
        conversationId: conversationId,
        updatedHistory: newConversation.history
      });
    } catch (error) {
      console.error('Error processing chat message:', error);
      next(error);
    }
  });

  // POST /api/chat/message/stream
  // Streaming endpoint for real-time LLM responses using Server-Sent Events
  router.options('/message/stream', (req, res) => {
    // Minimal preflight for dev UI on 5177
    res.header('Access-Control-Allow-Origin', 'http://localhost:5177');
    res.header('Access-Control-Allow-Methods', 'GET,POST,OPTIONS');
    // Allow a broad set of headers commonly sent by browsers/fetch
    res.header('Access-Control-Allow-Headers', 'Content-Type, Accept, Cache-Control, Pragma, Authorization, X-Requested-With');
    // Optional: how long the preflight can be cached
    res.header('Access-Control-Max-Age', '600');
    return res.sendStatus(204);
  });

  router.post('/message/stream', async (req, res, next) => {
    // Debug logging - uncomment for development
    // console.log('Received POST /api/chat/message/stream');
    // console.log('Request body:', JSON.stringify(req.body, null, 2));

    const { message, history, graphitiSettings } = req.body;

    if (!message) {
      console.log('Error: Message is required');
      return res.status(400).json({ error: 'Message is required' });
    }

    // Set headers for Server-Sent Events (JSON-only) BEFORE any write, set once
    res.setHeader('Content-Type', 'text/event-stream; charset=utf-8');
    res.setHeader('Cache-Control', 'no-cache, no-transform');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('X-Accel-Buffering', 'no');
    // CORS for dev UI
    res.setHeader('Access-Control-Allow-Origin', 'http://localhost:5177');
    res.setHeader('Access-Control-Allow-Methods', 'GET,POST,OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Accept, Cache-Control, Pragma, Authorization, X-Requested-With');
    if (typeof res.flushHeaders === 'function') res.flushHeaders();

    let finished = false;
    const safeWrite = (obj) => {
      if (finished) return;
      try {
        res.write(`data: ${JSON.stringify(obj)}\n\n`);
        // Force immediate flush for real-time streaming
        if (typeof res.flush === 'function') {
          res.flush();
        }
      } catch (_) {
        finished = true;
      }
    };
    const cleanup = () => {
      if (finished) return;
      finished = true;
      try { res.end(); } catch (_) {}
    };
    req.on('aborted', cleanup);
    res.on('close', cleanup);
    res.on('finish', () => { finished = true; });

    const requestId = `stream-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    console.log(`\n🚀 ========== STREAMING CHAT REQUEST ==========`);
    console.log(`🆔 Request ID: ${requestId}`);
    console.log(`📝 Question: "${message}"`);
    console.log(`📚 History Length: ${history ? history.length : 0} messages`);

    // Send initial connection event for immediate feedback
    safeWrite({ 
      type: 'connected', 
      timestamp: new Date().toISOString(),
      message: 'Processing your question...'
    });

    try {
      // Validate connections
      const validateConnections = async () => {
        if (!process.env.NEO4J_URI || !process.env.NEO4J_USER || !process.env.NEO4J_PASSWORD) {
          throw new Error('Missing Neo4j connection parameters in environment variables');
        }
        
        const ollamaUrl = graphitiSettings?.ollamaUrl || 'http://localhost:11434';
        try {
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 5000);
          
          const ollamaCheck = await fetch(`${ollamaUrl}/api/version`, { 
            method: 'GET',
            signal: controller.signal
          });
          
          clearTimeout(timeoutId);
          
          if (!ollamaCheck.ok) {
            throw new Error(`Ollama not accessible at ${ollamaUrl} (status: ${ollamaCheck.status})`);
          }
          console.log(`✅ Ollama connectivity verified at ${ollamaUrl}`);
        } catch (error) {
          if (error.name === 'AbortError') {
            throw new Error(`Ollama connectivity check timed out after 5 seconds at ${ollamaUrl}`);
          }
          throw new Error(`Ollama connectivity check failed: ${error.message}`);
        }
      };

      await validateConnections();

      // Use the streaming Python script
      const scriptPath = path.resolve('..', 'graphiti_search_engine.py');
      const pythonCmd = path.resolve('..', 'fastapi_env', 'bin', 'python');

      const scriptArgs = [
        scriptPath,
        message
      ];

      const resolvedSettings = {
        searchType: graphitiSettings?.searchType || 'COMBINED_HYBRID_SEARCH_CROSS_ENCODER',
        edgeCount: graphitiSettings?.edgeCount || 6,
        nodeCount: graphitiSettings?.nodeCount || 2,
        diversityFactor: graphitiSettings?.diversityFactor || 0.3,
        temperature: graphitiSettings?.temperature || 0.3
      };

      scriptArgs.push(
        '--search-type', resolvedSettings.searchType,
        '--edges', String(resolvedSettings.edgeCount),
        '--nodes', String(resolvedSettings.nodeCount),
        '--diversity', String(resolvedSettings.diversityFactor),
        '--temperature', String(resolvedSettings.temperature)
      );

      const pythonProcess = spawn(pythonCmd, scriptArgs, {
        cwd: path.resolve('..'),
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
          ...process.env,
          NEO4J_DATABASE: process.env.NEO4J_DATABASE || 'neo4j',
          OPENAI_API_KEY: process.env.OPENAI_API_KEY,
          GOOGLE_API_KEY: process.env.GOOGLE_API_KEY
        }
      });

      console.log(`🚀 Python process started with PID: ${pythonProcess.pid}`);

      // Collect all output for structured response capture
      let allOutput = '';
      let capturedStructuredResponse = null;
      let answerSection = '';
      let explanationSection = '';
      let followUpSection = '';
      let isCollectingAnswer = false;
      let isCollectingExplanation = false;
      let isCollectingFollowUp = false;

      // Normalize python stdout JSON lines to {type: delta|meta|final|close, ...}
      // Also stream non-JSON "thinking"/logs as incremental deltas so UI shows live progress
      pythonProcess.stdout.on('data', (data) => {
        const chunk = data.toString();
        allOutput += chunk;
        
        // Split while preserving chunk order
        const lines = chunk.split('\n').filter(line => line.trim() !== '');

        for (const raw of lines) {
          const trimmed = raw.trim();
          
          // Parse structured sections from Python output
          if (trimmed.startsWith('**Answer**')) {
            isCollectingAnswer = true;
            isCollectingExplanation = false;
            isCollectingFollowUp = false;
            continue;
          } else if (trimmed.startsWith('**Explanation**')) {
            isCollectingAnswer = false;
            isCollectingExplanation = true;
            isCollectingFollowUp = false;
            continue;
          } else if (trimmed.startsWith('**Follow Up Questions**') || trimmed.startsWith('**Follow up Questions**')) {
            isCollectingAnswer = false;
            isCollectingExplanation = false;
            isCollectingFollowUp = true;
            continue;
          }

          // Collect content into appropriate sections
          if (isCollectingAnswer && trimmed && !trimmed.startsWith('**')) {
            answerSection += trimmed + ' ';
            // Send answer text as delta for real-time display
            safeWrite({ type: 'delta', path: ['answer', 'text'], value: trimmed + ' ' });
          } else if (isCollectingExplanation && trimmed && !trimmed.startsWith('**')) {
            explanationSection += trimmed + ' ';
          } else if (isCollectingFollowUp && trimmed && !trimmed.startsWith('**') && trimmed.startsWith('- ')) {
            followUpSection += trimmed + '\n';
          }

          // Fast path: if it looks like JSON (starts with { or [), try to parse
          const likelyJson = trimmed.startsWith('{') || trimmed.startsWith('[');

          if (likelyJson) {
            try {
              const py = JSON.parse(trimmed);

              let out = null;

              // 1) Check for complete v2.0 structured response
              if (py.version === '2.0' && py.answer && py.entities !== undefined && py.sources !== undefined) {
                console.log(`🎯 Captured complete v2.0 structured response with ${py.entities?.length || 0} entities, ${py.sources?.length || 0} sources`);
                capturedStructuredResponse = py;
                out = { type: 'final', value: py };
              }

              // 2) Token/text chunk -> delta to answer.text
              if (!out && typeof py === 'object' && py !== null && (py.type === 'token' || py.type === 'text' || py.delta || py.content || py.text || py.token)) {
                const v = py.content || py.text || py.token || py.delta || '';
                out = { type: 'delta', path: ['answer', 'text'], value: String(v) };
              }

              // 3) References or metadata
              if (!out && (py.references || (py.type === 'references'))) {
                out = { type: 'meta', path: ['references'], value: py.references || py.value || [] };
              }

              // 4) Already a final structured response
              if (!out && (py.final || py.type === 'final' || (py.structured_response && py.structured_response.version))) {
                const finalObj = py.final || py.value || py.structured_response || py;
                out = { type: 'final', value: finalObj };
              }

              // 5) Close signal
              if (!out && (py.type === 'close' || py.close === true)) {
                out = { type: 'close' };
              }

              if (out) {
                safeWrite(out);
                continue;
              }
            } catch (e) {
              // fall-through to non-JSON handling below
            }
          }

          // Non-JSON line (progress/thinking/log). Stream it as a delta to a dedicated path.
          // This enables real-time "thinking" display without waiting for final JSON.
          if (trimmed.length > 0 && !isCollectingAnswer) {
            safeWrite({ type: 'delta', path: ['thinking'], value: trimmed + '\n' });
          }
        }
      });

      pythonProcess.stderr.on('data', (data) => {
        const errorMessage = data.toString();
        console.error(`❌ Python stderr: ${errorMessage}`);
        // Stream stderr as thinking lines unless it's clearly JSON
        const lines = errorMessage.split('\n').filter(l => l.trim() !== '');
        for (const l of lines) {
          const s = l.trim();
          if (s.startsWith('{') || s.startsWith('[')) {
            try {
              const obj = JSON.parse(s);
              safeWrite({ type: 'meta', path: ['stderr'], value: obj });
              continue;
            } catch (_) {
              // ignore and treat as text below
            }
          }
          safeWrite({ type: 'delta', path: ['thinking'], value: s + '\n' });
        }
      });

      pythonProcess.on('close', (code) => {
        console.log(`\n📊 Streaming completed with code: ${code}`);
        
        let finalStructuredResponse;

        if (capturedStructuredResponse) {
          // Use the complete structured response captured from Python script
          console.log(`✅ Using captured v2.0 structured response with full metadata`);
          finalStructuredResponse = capturedStructuredResponse;
          
          // Preserve thinking content
          const currentState = get();
          const thinkingContent = currentState.STREAM_THINKING_BUFFER?.thinking || '';
          finalStructuredResponse.thinking = thinkingContent;
        } else {
          // Fallback: construct basic structured response from collected sections
          console.log(`⚠️ No structured response captured, building fallback from text sections`);
          finalStructuredResponse = {
            version: '2.0',
            answer: answerSection.trim() || 'No answer content found',
            sections: explanationSection.trim() ? [
              {
                title: 'Explanation',
                content: explanationSection.trim()
              }
            ] : [],
            sources: [], // Will be populated by frontend if needed
            entities: [], // Will be populated by frontend if needed
            badges: [],
            follow_up: followUpSection.trim() ? 
              followUpSection.split('\n')
                .filter(line => line.trim().startsWith('- '))
                .map(line => line.replace(/^- /, '').trim())
                .filter(q => q.length > 0) : [],
            metadata: {
              processingTime: new Date().toISOString(),
              outputLength: allOutput.length,
              fallback: true
            }
          };
        }

        console.log(`📋 Sending final structured response:`, JSON.stringify({
          version: finalStructuredResponse.version,
          answer: finalStructuredResponse.answer?.substring(0, 100) + '...',
          entities: finalStructuredResponse.entities?.length || 0,
          sources: finalStructuredResponse.sources?.length || 0,
          badges: finalStructuredResponse.badges?.length || 0,
          follow_up: finalStructuredResponse.follow_up?.length || 0
        }, null, 2));

        // Send the final structured response (only if not already sent)
        if (!capturedStructuredResponse) {
          safeWrite({
            type: 'final',
            value: finalStructuredResponse
          });
        }

        // Ensure a final close event
        safeWrite({
          type: 'close',
          code: code,
          timestamp: new Date().toISOString()
        });
        cleanup();
      });

      pythonProcess.on('error', (error) => {
        console.error(`❌ Python process error:`, error);
        safeWrite({
          type: 'error',
          message: error.message,
          timestamp: new Date().toISOString()
        });
        cleanup();
      });

      req.on('close', () => {
        console.log(`🔌 Client disconnected, terminating Python process ${pythonProcess.pid}`);
        pythonProcess.kill('SIGTERM');
      });

    } catch (error) {
      console.error(`❌ Streaming setup error:`, error);
      safeWrite({
        type: 'error',
        message: error.message,
        timestamp: new Date().toISOString()
      });
      cleanup();
    }
  });

  // Function to call the FastAPI service for QA pipeline
  async function callPythonQAPipeline(question, history = [], graphitiSettings = {}) {
    const startTime = Date.now();
    const requestId = `chat-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

    try {
      const fastApiUrl = process.env.FASTAPI_URL || 'http://localhost:8000';
      
      // Transform complex history objects to simple {role, content} format for FastAPI
      const cleanHistory = (history || []).map(msg => {
        // Handle different message formats
        if (typeof msg === 'string') {
          try {
            msg = JSON.parse(msg);
          } catch (e) {
            return { role: 'user', content: msg };
          }
        }
        
        // Extract role and content, handling nested structures
        let role = msg.role || 'user';
        let content = msg.content || '';
        
        // If content is a complex object (like structured response), extract the actual text
        if (typeof content === 'object') {
          content = JSON.stringify(content);
        }
        
        return { role, content };
      }).filter(msg => msg.content.trim().length > 0); // Filter out empty messages
      
      const requestBody = {
        question: question,
        conversation_history: cleanHistory,
        graphiti_settings: {
          search_type: graphitiSettings.searchType || 'COMBINED_HYBRID_SEARCH_CROSS_ENCODER',
          edge_count: graphitiSettings.edgeCount || 6,
          node_count: graphitiSettings.nodeCount || 2,
          diversity_factor: graphitiSettings.diversityFactor || 0.3,
          temperature: graphitiSettings.temperature || 0.3,
          llm_provider: graphitiSettings.llmProvider || 'ollama',
          ollama_model: graphitiSettings.ollamaModel || 'gemma3:latest',
          ollama_url: graphitiSettings.ollamaUrl || 'http://localhost:11434'
        }
      };

      console.log(`📡 Calling FastAPI service at ${fastApiUrl}/chat`);
      console.log(`📝 Original history length: ${(history || []).length}, Cleaned history length: ${cleanHistory.length}`);
      console.log(`📝 Request body:`, JSON.stringify(requestBody, null, 2));

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout

      const response = await fetch(`${fastApiUrl}/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-Request-ID': requestId
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`FastAPI request failed with status ${response.status}: ${errorText}`);
      }

      const result = await response.json();
      const executionTime = Date.now() - startTime;

      console.log(`✅ FastAPI response received in ${executionTime}ms`);
      
      return {
        ...result,
        execution_time_ms: executionTime,
        request_id: requestId,
        structured_response: result.structured_response || result
      };

    } catch (error) {
      console.error(`❌ FastAPI call failed:`, error);
      if (error.name === 'AbortError') {
        throw new Error(`FastAPI service request timed out after 60 seconds`);
      }
      throw new Error(`FastAPI service error: ${error.message}`);
    }
  }

  // --- PostgreSQL-based conversation management endpoints ---

  /**
   * GET /api/chat/conversations
   * List all saved conversations from PostgreSQL
   */
  router.get('/conversations', async (req, res, next) => {
    try {
      const { page = 1, limit = 20, archived = false, search = '' } = req.query;
      
      let query = `
        SELECT cs.*, u.username, COUNT(m.id) as message_count
        FROM chat_sessions cs
        LEFT JOIN users u ON cs.user_id = u.id
        LEFT JOIN messages m ON cs.id = m.session_id
        WHERE cs.is_archived = $1
      `;
      
      const params = [archived === 'true'];
      
      if (search) {
        query += ` AND (cs.title ILIKE $${params.length + 1} OR cs.description ILIKE $${params.length + 1})`;
        params.push(`%${search}%`);
      }
      
      query += `
        GROUP BY cs.id, u.username
        ORDER BY cs.created_at DESC
        LIMIT $${params.length + 1} OFFSET $${params.length + 2}
      `;
      
      params.push(parseInt(limit), (parseInt(page) - 1) * parseInt(limit));
      
      const result = await pool.query(query, params);
      
      res.json({
        conversations: result.rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: parseInt(result.rows[0]?.total_count || 0)
        }
      });
    } catch (error) {
      next(error);
    }
  });

  /**
   * POST /api/chat/conversations
   * Create a new conversation in PostgreSQL
   */
  router.post('/conversations', async (req, res, next) => {
    try {
      const { title, description, user_id } = req.body;
      
      // Default to the admin user UUID from the database initialization
      const defaultUserId = 'ec540e89-4d5f-406e-8227-5a571147e7da';
      
      const newConversation = {
        user_id: user_id || defaultUserId,
        title: title || 'New Conversation',
        description: description || 'New conversation',
        is_active: true,
        is_archived: false,
        metadata: {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const insertQuery = `
        INSERT INTO chat_sessions (user_id, title, description, is_active, is_archived, metadata, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING *;
      `;
      
      const result = await pool.query(insertQuery, [
        newConversation.user_id,
        newConversation.title,
        newConversation.description,
        newConversation.is_active,
        newConversation.is_archived,
        JSON.stringify(newConversation.metadata),
        newConversation.created_at,
        newConversation.updated_at
      ]);

      res.status(201).json(result.rows[0]);
    } catch (error) {
      next(error);
    }
  });

  /**
   * GET /api/chat/conversations/:id
   * Retrieve a specific conversation from PostgreSQL
   */
  router.get('/conversations/:id', async (req, res, next) => {
    try {
      const { id } = req.params;
      
      const conversationQuery = `
        SELECT cs.*, u.username, COUNT(m.id) as message_count
        FROM chat_sessions cs
        LEFT JOIN users u ON cs.user_id = u.id
        LEFT JOIN messages m ON cs.id = m.session_id
        WHERE cs.id = $1
        GROUP BY cs.id, u.username;
      `;
      
      const conversationResult = await pool.query(conversationQuery, [id]);
      
      if (conversationResult.rows.length === 0) {
        return res.status(404).json({ error: 'Conversation not found' });
      }
      
      const messagesQuery = `
        SELECT * FROM messages
        WHERE session_id = $1
        ORDER BY created_at ASC;
      `;
      
      const messagesResult = await pool.query(messagesQuery, [id]);
      
      const conversation = {
        ...conversationResult.rows[0],
        messages: messagesResult.rows
      };
      
      res.json(conversation);
    } catch (error) {
      next(error);
    }
  });

  /**
   * PUT /api/chat/conversations/:id
   * Update a conversation in PostgreSQL
   */
  router.put('/conversations/:id', async (req, res, next) => {
    try {
      const { id } = req.params;
      const { title, description, is_active, is_archived, metadata } = req.body;
      
      const updateQuery = `
        UPDATE chat_sessions
        SET title = COALESCE($1, title),
            description = COALESCE($2, description),
            is_active = COALESCE($3, is_active),
            is_archived = COALESCE($4, is_archived),
            metadata = COALESCE($5, metadata),
            updated_at = NOW()
        WHERE id = $6
        RETURNING *;
      `;
      
      const result = await pool.query(updateQuery, [
        title,
        description,
        is_active,
        is_archived,
        JSON.stringify(metadata || {}),
        id
      ]);
      
      if (result.rows.length === 0) {
        return res.status(404).json({ error: 'Conversation not found' });
      }
      
      res.json(result.rows[0]);
    } catch (error) {
      next(error);
    }
  });

  /**
   * DELETE /api/chat/conversations/:id
   * Delete a conversation from PostgreSQL
   */
  router.delete('/conversations/:id', async (req, res, next) => {
    try {
      const { id } = req.params;
      
      const deleteQuery = `
        DELETE FROM chat_sessions
        WHERE id = $1
        RETURNING *;
      `;
      
      const result = await pool.query(deleteQuery, [id]);
      
      if (result.rows.length === 0) {
        return res.status(404).json({ error: 'Conversation not found' });
      }
      
      res.json({ message: 'Conversation deleted successfully' });
    } catch (error) {
      next(error);
    }
  });

  /**
   * GET /api/chat/conversations/:id/messages
   * Get messages for a specific conversation
   */
  router.get('/conversations/:id/messages', async (req, res, next) => {
    try {
      const { id } = req.params;
      const { page = 1, limit = 50 } = req.query;
      
      // Validate UUID format
      const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidPattern.test(id)) {
        return res.status(400).json({
          error: 'Invalid conversation ID format. Expected UUID format.',
          code: 'INVALID_CONVERSATION_ID',
          provided: id,
          timestamp: new Date().toISOString()
        });
      }
      
      const messagesQuery = `
        SELECT * FROM messages
        WHERE session_id = $1
        ORDER BY created_at ASC
        LIMIT $2 OFFSET $3;
      `;
      
      const messagesResult = await pool.query(messagesQuery, [
        id,
        parseInt(limit),
        (parseInt(page) - 1) * parseInt(limit)
      ]);
      
      res.json(messagesResult.rows);
    } catch (error) {
      next(error);
    }
  });

  /**
   * POST /api/chat/conversations/:id/messages
   * Add a message to a conversation
   */
  router.post('/conversations/:id/messages', async (req, res, next) => {
    try {
      const { id } = req.params;
      const { content, role = 'user', user_id = 'system', parent_message_id = null } = req.body;
      
      // If this is a user message, check if we need to update the conversation title
      if (role === 'user' && content && typeof content === 'string') {
        // Check if this conversation has a default title that should be updated
        const conversationQuery = `
          SELECT title, created_at, 
                 (SELECT COUNT(*) FROM messages WHERE session_id = $1 AND role = 'user') as user_message_count
          FROM chat_sessions WHERE id = $1;
        `;
        
        const conversationResult = await pool.query(conversationQuery, [id]);
        
        if (conversationResult.rows.length > 0) {
          const conversation = conversationResult.rows[0];
          const isFirstUserMessage = conversation.user_message_count === 0;
          const hasDefaultTitle = conversation.title === 'New Conversation' || 
                                 conversation.title.startsWith('Conversation from');
          
          // Update title if this is the first user message or if it has a default title
          if (isFirstUserMessage || hasDefaultTitle) {
            if (isValidForTitle(content)) {
              const smartTitle = generateTitleFromMessage(content);
              
              const updateTitleQuery = `
                UPDATE chat_sessions 
                SET title = $1, updated_at = NOW() 
                WHERE id = $2;
              `;
              
              await pool.query(updateTitleQuery, [smartTitle, id]);
              console.log(`✅ Updated conversation ${id} title to: ${smartTitle}`);
            }
          }
        }
      }
      
      const messageQuery = `
        INSERT INTO messages (session_id, user_id, role, content, parent_message_id, created_at)
        VALUES ($1, $2, $3, $4, $5, NOW())
        RETURNING *;
      `;
      
      const result = await pool.query(messageQuery, [
        id,
        user_id,
        role,
        content,
        parent_message_id
      ]);
      
      res.status(201).json(result.rows[0]);
    } catch (error) {
      next(error);
    }
  });

  return router;
};
