import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import chatApiService from '../services/chatApiService';
import { v4 as uuidv4 } from 'uuid';
import { validateConversationId, clearChatStorageCache } from '../utils/localStorage-cleanup';

// Helper function to generate unique IDs for optimistic updates
const generateId = () => uuidv4();

// Chat store state interface
const initialState = {
  // Conversation state
  conversations: [],
  currentConversation: null,
  isLoading: false,
  error: null,

  // Message state
  messages: [],
  isStreaming: false,
  streamingMessageId: null,

  // Streaming buffer state
  STREAM_THINKING_BUFFER: { thinking: '', answerText: '', active: false },

  // UI state
  sidebarOpen: true,
  isCreatingConversation: false,

  // Pagination
  pagination: {
    page: 1,
    limit: 20,
    hasMore: true,
    total: 0
  },

  // Search/filter
  searchQuery: '',
  filter: {
    archived: false,
    dateRange: null
  }
};

// Helper function to filter user-facing thinking content
function isUserFacingThinking(line) {
  const t = String(line || '').trim();
  if (!t) return false;

  const dropPrefixes = [
    '🔍','✅','⚙️','⏱️','📋',
    '- Graphiti LLM:', '- Response LLM:', '- Embeddings:',
    '- Search type:', '- Search:', '- Response:', '- Build:'
  ];
  if (dropPrefixes.some(p => t.startsWith(p))) return false;

  const dropContains = [
    'ResponseBuilder.', 'Node ', 'labels=[', 'group_id=', 'created_at=',
    'Successfully extracted entity:', "class 'graphiti_core", 'search_results type',
    'Search completed successfully', 'Top 3 entities:', 'Final entities after limit:',
    'Extracted '
  ];
  if (dropContains.some(p => t.includes(p))) return false;

  if (t === '' || t === '') return false;
  return true;
}

// Mirror answer into state for validator compatibility
const STREAM_ANSWER_MIRROR_ENABLED = true;
function _mirrorAnswerIntoState(state) {
  try {
    if (!STREAM_ANSWER_MIRROR_ENABLED) return;

    if (!state.answer) state.answer = {};
    if (typeof state.answer.text !== 'string') state.answer.text = '';

    if (state.STREAM_THINKING_BUFFER && typeof state.STREAM_THINKING_BUFFER.answerText === 'string') {
      state.answer.text = state.STREAM_THINKING_BUFFER.answerText;
    }

    if (!Array.isArray(state.sections)) state.sections = [];
    if (!Array.isArray(state.sources)) state.sources = [];
    if (!Array.isArray(state.entities)) state.entities = [];
    if (!Array.isArray(state.badges)) state.badges = [];
    if (!Array.isArray(state.follow_up)) state.follow_up = [];
    if (!state.version) state.version = '2.0';
  } catch (e) {
    // Silently ignore errors
  }
}

// SSE streaming reducer
export function applySseDeltaThinking(state, evt) {
  if (!state.STREAM_THINKING_BUFFER) {
    state.STREAM_THINKING_BUFFER = { thinking: '', answerText: '', active: false };
  }
  const buf = state.STREAM_THINKING_BUFFER;

  if (evt && evt.type === 'delta') {
    const path = Array.isArray(evt.path) ? evt.path : [];

    if (path[0] === 'thinking') {
      const chunks = String(evt.value || '').split(/\r?\n/);
      for (const raw of chunks) {
        const line = raw.trim();
        if (isUserFacingThinking(line)) {
          if (buf.thinking && !buf.thinking.endsWith('\n')) buf.thinking += '\n';
          buf.thinking += line + '\n';
          buf.active = true;
        }
      }
    } else if (path[0] === 'answer' && path[1] === 'text') {
      buf.answerText += (evt.value || '');
      buf.active = true;
    }

    _mirrorAnswerIntoState(state);
  } else if (evt && evt.type === 'final') {
    state.lastFinal = evt.value;
  } else if (evt && evt.type === 'close') {
    buf.active = false;
  }
}

// Chat store actions
export const useChatStore = create(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        // Conversation actions
        setConversations: (conversations) => set({ conversations }),
        
        setCurrentConversation: (conversation) => set({ currentConversation: conversation }),
        
        createConversation: async (title = 'New Conversation', description = '') => {
          const tempId = generateId();
          
          // Optimistic update
          const optimisticConversation = {
            id: tempId,
            title,
            description,
            is_active: true,
            is_archived: false,
            metadata: {},
            messages: [],
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };
          
          set(state => ({
            conversations: [optimisticConversation, ...state.conversations],
            currentConversation: optimisticConversation,
            isCreatingConversation: true
          }));
          
          try {
            const conversation = await chatApiService.createConversation(
              title,
              description
            );
            
            // Replace optimistic conversation with real one and reload conversations list
            set(state => ({
              conversations: state.conversations.map(c => 
                c.id === tempId ? conversation : c
              ),
              currentConversation: state.currentConversation?.id === tempId ? conversation : state.currentConversation,
              isCreatingConversation: false
            }));
            
            // Refresh the conversations list to ensure it's up to date
            get().loadConversations();
            
            return conversation;
          } catch (error) {
            // Rollback on error
            set(state => ({
              conversations: state.conversations.filter(c => c.id !== tempId),
              currentConversation: state.currentConversation?.id === tempId ? null : state.currentConversation,
              isCreatingConversation: false,
              error: error.message
            }));
            throw error;
          }
        },
        
        updateConversation: async (id, updates) => {
          const { conversations } = get();
          const conversation = conversations.find(c => c.id === id);
          if (!conversation) return;
          
          // Optimistic update
          set(state => ({
            conversations: state.conversations.map(c => 
              c.id === id ? { ...c, ...updates } : c
            ),
            currentConversation: state.currentConversation?.id === id 
              ? { ...state.currentConversation, ...updates }
              : state.currentConversation
          }));
          
          try {
            const updated = await chatApiService.updateConversation(id, updates);
            set(state => ({
              conversations: state.conversations.map(c => 
                c.id === id ? updated : c
              ),
              currentConversation: state.currentConversation?.id === id ? updated : state.currentConversation
            }));
            return updated;
          } catch (error) {
            // Rollback on error
            set(state => ({
              conversations: state.conversations.map(c => 
                c.id === id ? conversation : c
              ),
              currentConversation: state.currentConversation?.id === id ? conversation : state.currentConversation,
              error: error.message
            }));
            throw error;
          }
        },
        
        deleteConversation: async (id) => {
          const { conversations, currentConversation } = get();
          const conversation = conversations.find(c => c.id === id);
          if (!conversation) return;
          
          // Optimistic update
          set(state => ({
            conversations: state.conversations.filter(c => c.id !== id),
            currentConversation: state.currentConversation?.id === id ? null : state.currentConversation
          }));
          
          try {
            await chatApiService.deleteConversation(id);
          } catch (error) {
            // Rollback on error
            set(state => ({
              conversations: [...state.conversations, conversation].sort((a, b) => 
                new Date(b.created_at) - new Date(a.created_at)
              ),
              currentConversation: state.currentConversation === null && currentConversation?.id === id 
                ? conversation 
                : state.currentConversation,
              error: error.message
            }));
            throw error;
          }
        },
        
        archiveConversation: async (id) => {
          return get().updateConversation(id, { is_archived: true });
        },
        
        unarchiveConversation: async (id) => {
          return get().updateConversation(id, { is_archived: false });
        },
        
        loadConversations: async (page = 1, limit = 20) => {
          set({ isLoading: true, error: null });
          
          try {
            const result = await chatApiService.getConversations({
              page,
              limit,
              archived: get().filter.archived,
              search: get().searchQuery
            });
            
            // Handle both old and new API response formats
            const conversations = result.conversations || result || [];
            const total = result.total || conversations.length;
            
            set({
              conversations: page === 1 ? conversations : [...get().conversations, ...conversations],
              pagination: {
                page,
                limit,
                hasMore: conversations.length === limit,
                total
              },
              isLoading: false
            });
            
            return conversations;
          } catch (error) {
            set({ error: error.message, isLoading: false });
            throw error;
          }
        },
        
        loadConversation: async (id) => {
          set({ isLoading: true, error: null });
          
          try {
            const conversation = await chatApiService.getConversation(id);
            const messages = await chatApiService.getMessages(id);
            
            set({
              currentConversation: { ...conversation, messages },
              messages,
              isLoading: false
            });
            
            return { conversation, messages };
          } catch (error) {
            set({ error: error.message, isLoading: false });
            throw error;
          }
        },
        
        // Message actions
        setMessages: (messages) => set({ messages }),
        
        addMessage: (message) => set(state => ({
          messages: [...state.messages, message]
        })),
        
        sendMessage: async (content, parentMessageId = null) => {
          const { currentConversation, messages } = get();
          if (!currentConversation) throw new Error('No active conversation');
          
          const tempId = generateId();
          const userMessage = {
            id: tempId,
            session_id: currentConversation.id,
            role: 'user',
            content,
            parent_message_id: parentMessageId,
            created_at: new Date().toISOString()
          };
          
          // Add user message optimistically
          set(state => ({
            messages: [...state.messages, userMessage]
          }));
          
          try {
            // Send message and get response
            const apiResponse = await chatApiService.sendMessage(
              content,
              messages,
              tempId,
              currentConversation.graphitiSettings
            );
            
            // Validate API response structure
            if (!apiResponse || !apiResponse.response) {
              throw new Error('Invalid API response: missing response object');
            }
            
            if (!apiResponse.response.role || !apiResponse.response.content) {
              throw new Error('Invalid API response: missing role or content');
            }
            
            // Extract the actual assistant message from the API response
            const assistantMessage = {
              id: generateId(),
              session_id: currentConversation.id,
              role: apiResponse.response.role,
              content: apiResponse.response.content,
              created_at: apiResponse.response.timestamp || new Date().toISOString(),
              sourceDocuments: apiResponse.response.sourceDocuments || [],
              sourceNodes: apiResponse.response.sourceNodes || []
            };
            
            // Add assistant response
            set(state => ({
              messages: [...state.messages, assistantMessage]
            }));
            
            // Log success in development
            if (process.env.NODE_ENV === 'development') {
              console.log('✅ Message sent successfully:', {
                userMessage: userMessage.content,
                assistantResponse: assistantMessage.content.substring(0, 100) + '...',
                conversationId: currentConversation.id
              });
            }
            
            return { userMessage, assistantMessage };
          } catch (error) {
            // Remove user message on error
            set(state => ({
              messages: state.messages.filter(m => m.id !== tempId),
              error: error.message
            }));
            throw error;
          }
        },
        
        sendStreamingMessage: async (content, parentMessageId = null, onChunk = null) => {
          const { currentConversation, messages } = get();
          if (!currentConversation) throw new Error('No active conversation');

          const tempUserId = generateId();
          const tempAssistantId = generateId();

          const userMessage = {
            id: tempUserId,
            session_id: currentConversation.id,
            role: 'user',
            content,
            parent_message_id: parentMessageId,
            created_at: new Date().toISOString()
          };

          const assistantMessage = {
            id: tempAssistantId,
            session_id: currentConversation.id,
            role: 'assistant',
            content: '',
            parent_message_id: tempUserId,
            created_at: new Date().toISOString(),
            sourceDocuments: [],
            sourceNodes: []
          };

          // Add messages optimistically
          set(state => ({
            messages: [...state.messages, userMessage, assistantMessage],
            isStreaming: true,
            streamingMessageId: tempAssistantId
          }));

          try {
            const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3003/api';
            const streamingUrl = `${API_URL}/chat/message/stream`;

            const payload = {
              message: content,
              history: messages.map(m => ({ role: m.role, content: m.content })),
              graphitiSettings: currentConversation.graphitiSettings || {}
            };

            // v2.0 skeleton object to build incrementally - properly structured for validation
            const currentObj = {
              version: '2.0',
              answer: '', // String format as required by V2 schema
              sections: [],
              sources: [],
              entities: [],
              badges: [],
              follow_up: [],
              metadata: {}
            };

            const response = await fetch(streamingUrl, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'text/event-stream',
                'Cache-Control': 'no-cache',
              },
              body: JSON.stringify(payload)
            });

            if (!response.ok) {
              throw new Error(`Streaming request failed with status ${response.status}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            // Track streaming state
            let isStreamingActive = true;

            const updateAssistantContent = (content, isStreaming = true, thinkingContent = null) => {
              set(state => ({
                messages: state.messages.map(m =>
                  m.id === tempAssistantId ? {
                    ...m,
                    content,
                    isStreaming: isStreaming,
                    // Preserve thinking content when streaming completes
                    thinkingContent: thinkingContent || m.thinkingContent
                  } : m
                ),
                isStreaming: isStreaming,
                streamingMessageId: isStreaming ? tempAssistantId : null
              }));
            };

            // Function to update assistant message with current streaming content
            const updateStreamingContent = () => {
              const currentState = get();
              const buf = currentState.STREAM_THINKING_BUFFER;

              // Create a partial response object for real-time display - V2 compliant format
              const partialResponse = {
                version: '2.0',
                answer: buf?.answerText || '', // String format as required by V2 schema
                sections: [],
                sources: [],
                entities: [],
                badges: [],
                follow_up: [],
                metadata: { streaming: true }
              };

              // Only update if we have meaningful content
              if (buf?.answerText || buf?.thinking) {
                updateAssistantContent(JSON.stringify(partialResponse), true);
              }
            };

            const readStream = async () => {
              let doneFinal = false;
              while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value, { stream: true });
                const lines = chunk.split('\n');

                for (const line of lines) {
                  const trimmed = line.trim();
                  if (!trimmed || !trimmed.startsWith('data: ')) continue;

                  try {
                    const evt = JSON.parse(trimmed.slice(6)); // strip 'data: '
                    if (evt.type === 'error') {
                      throw new Error(evt.message || 'Streaming error occurred');
                    }

                    // Use applySseDeltaThinking to handle all streaming events
                    set(state => {
                      const newState = { ...state };
                      applySseDeltaThinking(newState, evt);
                      return newState;
                    });

                    if (evt.type === 'delta') {
                      // Handle answer text deltas for the v2.0 object
                      const path = Array.isArray(evt.path) ? evt.path : [];
                      if (path[0] === 'answer' && path[1] === 'text') {
                        currentObj.answer += String(evt.value || ''); // Direct string concatenation
                      }

                      // Update streaming content in real-time
                      updateStreamingContent();

                      if (onChunk && typeof onChunk === 'function') onChunk(evt.value);
                    } else if (evt.type === 'meta') {
                      // Handle metadata and other V2 fields
                      const path = Array.isArray(evt.path) ? evt.path : [];
                      if (path[0] === 'sources') {
                        currentObj.sources = Array.isArray(evt.value) ? evt.value : currentObj.sources;
                      } else if (path[0] === 'entities') {
                        currentObj.entities = Array.isArray(evt.value) ? evt.value : currentObj.entities;
                      } else if (path[0] === 'badges') {
                        currentObj.badges = Array.isArray(evt.value) ? evt.value : currentObj.badges;
                      } else if (path[0] === 'sections') {
                        currentObj.sections = Array.isArray(evt.value) ? evt.value : currentObj.sections;
                      } else if (path[0] === 'follow_up') {
                        currentObj.follow_up = Array.isArray(evt.value) ? evt.value : currentObj.follow_up;
                      }
                    } else if (evt.type === 'final') {
                      // Replace the whole content with final value and end streaming
                      const finalObj = evt.value && typeof evt.value === 'object'
                        ? evt.value
                        : currentObj;
                      
                      // Capture thinking content from current streaming state
                      const currentState = get();
                      const thinkingContent = currentState.STREAM_THINKING_BUFFER?.thinking || '';
                      
                      const json = JSON.stringify(finalObj);
                      updateAssistantContent(json, false, thinkingContent); // Mark as not streaming and preserve thinking
                      doneFinal = true;
                    } else if (evt.type === 'close') {
                      // End of stream
                      const currentState = get();
                      const thinkingContent = currentState.STREAM_THINKING_BUFFER?.thinking || '';
                      updateAssistantContent(JSON.stringify(currentObj), false, thinkingContent);
                      return;
                    }
                  } catch (e) {
                    // Ignore parse errors of partial frames and continue
                    if (process.env.NODE_ENV === 'development') {
                      console.warn('Stream parse warning:', e, line);
                    }
                  }
                }
              }

              // If no final sent, finalize with current object
              if (!doneFinal) {
                const currentState = get();
                const thinkingContent = currentState.STREAM_THINKING_BUFFER?.thinking || '';
                updateAssistantContent(JSON.stringify(currentObj), false, thinkingContent);
              }
            };

            await readStream();

            set(() => ({
              isStreaming: false,
              streamingMessageId: null
            }));

            // Return both messages
            return {
              userMessage,
              assistantMessage: get().messages.find(m => m.id === tempAssistantId)
            };

          } catch (error) {
            // Remove messages on error
            set(state => ({
              messages: state.messages.filter(m => m.id !== tempUserId && m.id !== tempAssistantId),
              isStreaming: false,
              streamingMessageId: null,
              error: error.message
            }));

            if (process.env.NODE_ENV === 'development') {
              console.error('❌ Streaming message failed:', {
                error: error.message,
                conversationId: currentConversation.id,
                userMessage: content
              });
            }

            throw error;
          }
        },
        
        loadMessages: async (conversationId, page = 1, limit = 50) => {
          set({ isLoading: true, error: null });
          
          // Validate conversation ID format
          if (!validateConversationId(conversationId)) {
            console.error('❌ Invalid conversation ID format:', conversationId);
            set({ 
              error: 'Invalid conversation ID format. Please refresh and try again.', 
              isLoading: false 
            });
            return [];
          }
          
          try {
            const messages = await chatApiService.getMessages(conversationId, {
              page,
              limit
            });
            
            set({
              messages: page === 1 ? messages : [...messages, ...get().messages],
              isLoading: false
            });
            
            return messages;
          } catch (error) {
            console.error('❌ Error loading messages for conversation:', conversationId, error);
            set({ error: error.message, isLoading: false });
            throw error;
          }
        },
        
        // UI actions
        setSidebarOpen: (open) => set({ sidebarOpen: open }),
        
        setSearchQuery: (query) => set({ searchQuery: query }),
        
        setFilter: (filter) => set({ filter }),
        
        clearError: () => set({ error: null }),
        
        reset: () => set(initialState)
      }),
      {
        name: 'chat-store',
        partialize: (state) => {
          // Clean function to remove circular references and non-serializable data
          const cleanConversation = (conv) => {
            if (!conv) return null;
            return {
              id: conv.id,
              title: conv.title,
              description: conv.description,
              is_active: conv.is_active,
              is_archived: conv.is_archived,
              metadata: conv.metadata || {},
              created_at: conv.created_at,
              updated_at: conv.updated_at,
              user_id: conv.user_id,
              username: conv.username,
              message_count: conv.message_count
              // Exclude messages array and any potential circular references
            };
          };

          const cleanConversations = (conversations) => {
            if (!Array.isArray(conversations)) return [];
            return conversations.map(cleanConversation).filter(Boolean);
          };

          return {
            conversations: cleanConversations(state.conversations),
            currentConversation: cleanConversation(state.currentConversation),
            sidebarOpen: state.sidebarOpen,
            searchQuery: state.searchQuery,
            filter: state.filter ? {
              archived: state.filter.archived,
              dateRange: state.filter.dateRange
            } : { archived: false, dateRange: null }
          };
        }
      }
    )
  )
);

// Selectors
export const useChatSelectors = {
  useConversations: () => useChatStore(state => state.conversations),
  useCurrentConversation: () => useChatStore(state => state.currentConversation),
  useMessages: () => useChatStore(state => state.messages),
  useIsLoading: () => useChatStore(state => state.isLoading),
  useIsStreaming: () => useChatStore(state => state.isStreaming),
  useError: () => useChatStore(state => state.error),
  useSidebarOpen: () => useChatStore(state => state.sidebarOpen),
  useSearchQuery: () => useChatStore(state => state.searchQuery),
  useFilter: () => useChatStore(state => state.filter),
  usePagination: () => useChatStore(state => state.pagination),
  useHasMore: () => useChatStore(state => state.pagination.hasMore),
  useTotalConversations: () => useChatStore(state => state.pagination.total),
  useIsCreatingConversation: () => useChatStore(state => state.isCreatingConversation),
  useStreamingMessageId: () => useChatStore(state => state.streamingMessageId)
};

// Actions
export const useChatActions = {
  createConversation: () => useChatStore.getState().createConversation,
  updateConversation: () => useChatStore.getState().updateConversation,
  deleteConversation: () => useChatStore.getState().deleteConversation,
  archiveConversation: () => useChatStore.getState().archiveConversation,
  unarchiveConversation: () => useChatStore.getState().unarchiveConversation,
  loadConversations: () => useChatStore.getState().loadConversations,
  loadConversation: () => useChatStore.getState().loadConversation,
  sendMessage: () => useChatStore.getState().sendMessage,
  sendStreamingMessage: () => useChatStore.getState().sendStreamingMessage,
  loadMessages: () => useChatStore.getState().loadMessages,
  setSidebarOpen: () => useChatStore.getState().setSidebarOpen,
  setSearchQuery: () => useChatStore.getState().setSearchQuery,
  setFilter: () => useChatStore.getState().setFilter,
  clearError: () => useChatStore.getState().clearError,
  reset: () => useChatStore.getState().reset
};
