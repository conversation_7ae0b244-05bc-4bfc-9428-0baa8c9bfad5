import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.jsx'
import ErrorBoundary from './components/ErrorBoundary.jsx'
import './styles/360t-theme.css'
import './index.css'
import './styles/override.css'
/* Removed legacy component-level NodeDetailsModern.css to avoid !important overrides that break scrolling */
/* import './components/NodeDetailsModern.css' */
import './components/chat/StructuredResponse.css'
import './styles/ChatView.css'

// Perform localStorage cleanup on app startup to fix invalid search types
import { performStartupCleanup } from './utils/localStorageCleanup.js'
performStartupCleanup()

// Import conversation ID cleanup utility (auto-clears stale conversation cache in dev)
import './utils/localStorage-cleanup.js';

ReactDOM.createRoot(document.getElementById('root')).render(
  // Temporarily disabled React.StrictMode to fix infinite re-rendering issue
  // <React.StrictMode>
    <ErrorBoundary>
      <App />
    </ErrorBoundary>
  // </React.StrictMode>,
)
