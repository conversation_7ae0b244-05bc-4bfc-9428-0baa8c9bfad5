/**
 * Response Validator for v2.0 Structured Chat Responses
 * Provides schema validation and enhanced error reporting
 */

// Simplified schema validation for frontend (without full JSON Schema dependency)
const validateV2Response = (response, options = {}) => {
  const errors = [];
  const { isStreaming = false } = options;

  // Check basic structure
  if (!response || typeof response !== 'object') {
    errors.push('Response must be an object');
    return { isValid: false, errors };
  }

  // Required fields validation
  const requiredFields = ['version', 'answer', 'sections', 'sources', 'entities', 'badges', 'follow_up'];
  for (const field of requiredFields) {
    if (!(field in response)) {
      errors.push(`Missing required field: ${field}`);
    }
  }

  // Version validation
  if (response.version !== '2.0') {
    errors.push(`Invalid version: expected "2.0", got "${response.version}"`);
  }

  // Answer validation - more lenient for streaming responses
  if (typeof response.answer !== 'string') {
    errors.push('Answer must be a string');
  } else if (!isStreaming && response.answer.length === 0) {
    errors.push('Answer must be a non-empty string');
  }
  
  // Array fields validation
  const arrayFields = ['sections', 'sources', 'entities', 'badges', 'follow_up'];
  for (const field of arrayFields) {
    if (!Array.isArray(response[field])) {
      errors.push(`${field} must be an array`);
    }
  }
  
  // Sections validation
  if (Array.isArray(response.sections)) {
    response.sections.forEach((section, index) => {
      if (!section.title || typeof section.title !== 'string') {
        errors.push(`Section ${index}: title must be a non-empty string`);
      }
      if (!section.content || typeof section.content !== 'string') {
        errors.push(`Section ${index}: content must be a non-empty string`);
      }
      if (!Array.isArray(section.entity_refs)) {
        errors.push(`Section ${index}: entity_refs must be an array`);
      }
    });
  }
  
  // Sources validation
  if (Array.isArray(response.sources)) {
    response.sources.forEach((source, index) => {
      if (!source.id || typeof source.id !== 'string') {
        errors.push(`Source ${index}: id must be a non-empty string`);
      }
      if (!source.title || typeof source.title !== 'string') {
        errors.push(`Source ${index}: title must be a non-empty string`);
      }
    });
  }
  
  // Entities validation
  if (Array.isArray(response.entities)) {
    response.entities.forEach((entity, index) => {
      if (!entity.id || typeof entity.id !== 'string') {
        errors.push(`Entity ${index}: id must be a non-empty string`);
      }
      if (!entity.name || typeof entity.name !== 'string') {
        errors.push(`Entity ${index}: name must be a non-empty string`);
      }
      if (typeof entity.relevance_score !== 'number' || entity.relevance_score < 0 || entity.relevance_score > 1) {
        errors.push(`Entity ${index}: relevance_score must be a number between 0 and 1`);
      }
    });
  }
  
  // Badges validation
  if (Array.isArray(response.badges)) {
    response.badges.forEach((badge, index) => {
      if (!badge.label || typeof badge.label !== 'string') {
        errors.push(`Badge ${index}: label must be a non-empty string`);
      }
      if (!badge.tooltip || typeof badge.tooltip !== 'string') {
        errors.push(`Badge ${index}: tooltip must be a non-empty string`);
      }
    });
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warningCount: 0, // Could add warning-level validations later
    fieldCounts: {
      sections: response.sections?.length || 0,
      sources: response.sources?.length || 0,
      entities: response.entities?.length || 0,
      badges: response.badges?.length || 0,
      followUp: response.follow_up?.length || 0
    }
  };
};

/**
 * Enhanced response parser with validation
 * @param {string} content - Raw JSON content to parse
 * @param {Object} options - Validation options
 * @returns {Object} - Parsed and validated response
 */
export const parseAndValidateResponse = (content, options = {}) => {
  const result = {
    success: false,
    data: null,
    error: null,
    validationResult: null,
    debugInfo: {
      contentLength: content?.length || 0,
      contentType: typeof content,
      parseAttempted: false,
      validationAttempted: false,
      isStreaming: options.isStreaming || false
    }
  };

  // Basic content validation
  if (!content || typeof content !== 'string') {
    result.error = `Invalid content: expected string, got ${typeof content}`;
    return result;
  }

  // Attempt JSON parsing
  try {
    result.debugInfo.parseAttempted = true;
    const parsed = JSON.parse(content);
    result.data = parsed;

    // Check if this is a streaming response
    const isStreaming = options.isStreaming || parsed?.metadata?.streaming === true;

    // Validate the parsed response with streaming awareness
    result.debugInfo.validationAttempted = true;
    result.validationResult = validateV2Response(parsed, { isStreaming });

    if (result.validationResult.isValid) {
      result.success = true;
    } else {
      result.error = `Validation failed: ${result.validationResult.errors.join(', ')}`;
    }

    return result;
  } catch (parseError) {
    result.error = `JSON parse error: ${parseError.message}`;
    result.debugInfo.parseError = parseError.message;
    return result;
  }
};

/**
 * Quick validation check for v2.0 response format
 * @param {string} content - Raw JSON content
 * @returns {boolean} - True if valid v2.0 response
 */
export const isValidV2Response = (content) => {
  const result = parseAndValidateResponse(content);
  return result.success;
};

/**
 * Development-only debug logger for response validation
 * @param {Object} validationResult - Result from parseAndValidateResponse
 * @param {string} context - Context string for logging
 */
export const logValidationDebug = (validationResult, context = 'Response') => {
  if (process.env.NODE_ENV !== 'development') return;
  
  console.log(`🔍 ${context} Validation Debug:`, {
    success: validationResult.success,
    error: validationResult.error,
    fieldCounts: validationResult.validationResult?.fieldCounts,
    errorCount: validationResult.validationResult?.errors?.length || 0,
    debugInfo: validationResult.debugInfo
  });
  
  // Log validation errors if any
  if (validationResult.validationResult?.errors?.length > 0) {
    console.warn(`⚠️ ${context} Validation Errors:`, validationResult.validationResult.errors);
  }
};

export default {
  validateV2Response,
  parseAndValidateResponse,
  isValidV2Response,
  logValidationDebug
};