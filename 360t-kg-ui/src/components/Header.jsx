import React, { useState } from 'react';
import * as d3 from 'd3';
import { getInitialGraph } from '../services/api';
import '../styles/360t-theme.css';
import '../styles/ModernHeader.css';
import logo from '../assets/logos/360T-logo.png';
import GraphitiConfigPanel from './GraphitiConfigPanel';

/**
 * Modern Header component with enhanced navigation, breadcrumb navigation, and improved 2D/3D toggle
 */
function Header({ currentView, onSwitchView, is3DMode, onToggle3DMode }) {
  const [showGraphitiConfig, setShowGraphitiConfig] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Check if 3D mode is enabled via environment variable
  const enable3DMode = import.meta.env.VITE_ENABLE_3D_MODE === 'true';

  // Enhanced cleanup function for tooltips
  const cleanupTooltips = () => {
    // Remove all D3 tooltips
    d3.select("body").selectAll(".document-tooltip").remove();
    // Remove React tooltips  
    document.querySelectorAll('.custom-tooltip, .node-chip-tooltip').forEach(el => el.remove());
  };

  const handleNavClick = (view) => {
    // Clean up tooltips before switching views
    cleanupTooltips();
    
    if (onSwitchView) {
      onSwitchView(view);
      
      // For explorer view, also trigger initial graph loading
      // This ensures the full graph is displayed when switching to explorer
      if (view === 'explorer') {
        // Use a delay to ensure the view state is updated first
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('loadInitialGraph'));
        }, 100);
      }
    } else {
      // Fallback for standalone use (optional)
      let eventName;
      switch (view) {
        case 'explorer':
          eventName = 'loadInitialGraph';
          break;
        case 'analysis':
          eventName = 'showAnalysis';
          break;
        case 'documentation':
          eventName = 'showDocumentation';
          break;
        case 'chat':
          eventName = 'showChat';
          break;
        default:
          return;
      }
      window.dispatchEvent(new CustomEvent(eventName));
    }
  };

  // Get view metadata for breadcrumb and context
  const getViewMetadata = (view) => {
    const metadata = {
      chat: { 
        title: 'AI Chat',
        description: 'Interact with the knowledge graph using natural language',
        icon: 'M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z'
      },
      explorer: { 
        title: 'Graph Explorer',
        description: 'Visualize and explore the knowledge graph structure',
        icon: 'M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15'
      },
      analysis: { 
        title: 'Advanced Analysis',
        description: 'Perform deep analysis and discover hidden patterns',
        icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z'
      },
      documentation: { 
        title: 'Documentation',
        description: 'Browse system documentation and guides',
        icon: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
      }
    };
    return metadata[view] || { title: view, description: '', icon: '' };
  };

  const currentViewMeta = getViewMetadata(currentView);

  return (
    <header className="modern-header">
      {/* Main Header Bar */}
      <div className="header-main">
        {/* Logo and Brand Section */}
        <div className="header-brand">
          <div className="logo-container">
            <img src={logo} alt="360T Logo" className="brand-logo" />
            <div className="brand-text">
              <h1 className="brand-title">Knowledge Graph</h1>
              <span className="brand-subtitle">360T Platform Analytics</span>
            </div>
          </div>
        </div>

        {/* Primary Navigation */}
        <nav className={`header-nav ${isMobileMenuOpen ? 'nav-open' : ''}`}>
          <ul className="nav-list">
            <li className="nav-item">
              <button 
                className={`nav-button ${currentView === 'chat' ? 'active' : ''}`} 
                onClick={() => handleNavClick('chat')}
                aria-current={currentView === 'chat' ? 'page' : undefined}
              >
                <svg className="nav-icon" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d={getViewMetadata('chat').icon} />
                </svg>
                <span className="nav-text">Chat</span>
              </button>
            </li>
            <li className="nav-item">
              <button 
                className={`nav-button ${currentView === 'explorer' ? 'active' : ''}`} 
                onClick={() => handleNavClick('explorer')}
                aria-current={currentView === 'explorer' ? 'page' : undefined}
              >
                <svg className="nav-icon" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d={getViewMetadata('explorer').icon} />
                </svg>
                <span className="nav-text">Explorer</span>
              </button>
            </li>
            <li className="nav-item">
              <button 
                className={`nav-button ${currentView === 'analysis' ? 'active' : ''}`} 
                onClick={() => handleNavClick('analysis')}
                aria-current={currentView === 'analysis' ? 'page' : undefined}
              >
                <svg className="nav-icon" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d={getViewMetadata('analysis').icon} />
                </svg>
                <span className="nav-text">Analysis</span>
              </button>
            </li>
          </ul>
        </nav>

        {/* Action Controls */}
        <div className="header-actions">
          {/* Enhanced 2D/3D Toggle - prominently displayed when available */}
          {enable3DMode && currentView === 'explorer' && (
            <div className="view-mode-toggle">
              <div className="toggle-group" role="group" aria-label="View mode selection">
                <button
                  className={`toggle-button ${!is3DMode ? 'active' : ''}`}
                  onClick={() => onToggle3DMode && onToggle3DMode(false)}
                  aria-pressed={!is3DMode}
                  title="Switch to 2D View"
                >
                  <span>2D</span>
                </button>
                <button
                  className={`toggle-button ${is3DMode ? 'active' : ''}`}
                  onClick={() => onToggle3DMode && onToggle3DMode(true)}
                  aria-pressed={is3DMode}
                  title="Switch to 3D View"
                >
                  <span>3D</span>
                </button>
              </div>
            </div>
          )}

          {/* Secondary Actions */}
          <div className="secondary-actions">
            <button
              className="action-button settings-button"
              title="Graphiti Search Settings"
              onClick={() => setShowGraphitiConfig(true)}
            >
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="3"></circle>
                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
              </svg>
            </button>
            <button
              className="action-button help-button"
              title="Help & Documentation"
              onClick={() => {
                try {
                  if (typeof onSwitchView === 'function') {
                    onSwitchView('documentation');
                  } else {
                    window.dispatchEvent(new CustomEvent('showDocumentation'));
                  }
                } catch {
                  window.open('/docs/ui-design-specs/README.md', '_self');
                }
              }}
              aria-label="Help & Documentation"
            >
              <svg width="18" height="18" viewBox="0 0 24 24" aria-hidden="true" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M9.09 9a3 3 0 1 1 5.82 1c-.46.87-1.17 1.3-1.92 1.76-.69.42-1 .87-1 1.74V14"></path>
                <path d="M12 18h.01"></path>
              </svg>
            </button>
          </div>

          {/* Mobile Menu Toggle */}
          <button 
            className="mobile-menu-toggle"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            aria-expanded={isMobileMenuOpen}
            aria-label="Toggle navigation menu"
          >
            <span className={`menu-line ${isMobileMenuOpen ? 'open' : ''}`}></span>
            <span className={`menu-line ${isMobileMenuOpen ? 'open' : ''}`}></span>
            <span className={`menu-line ${isMobileMenuOpen ? 'open' : ''}`}></span>
          </button>
        </div>
      </div>


      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div 
          className="mobile-menu-overlay"
          onClick={() => setIsMobileMenuOpen(false)}
          aria-hidden="true"
        />
      )}

      {/* Graphiti Configuration Panel */}
      <GraphitiConfigPanel 
        isOpen={showGraphitiConfig}
        onClose={() => setShowGraphitiConfig(false)}
      />
    </header>
  );
}

export default Header;
