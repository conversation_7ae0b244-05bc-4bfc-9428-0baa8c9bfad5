import React, { useState, useMemo } from 'react';
import MarkdownRenderer from '../MarkdownRenderer';
import RichTextRenderer from './RichTextRenderer';
import AnswerWithReferences from './AnswerWithReferences';
import SourceTooltip from './SourceTooltip';
import EntityList from './EntityList';
import ThinkingSection from './ThinkingSection';
import FollowUpCards from './FollowUpCards';

/**
 * StructuredResponse component for displaying v2.0 structured chat responses
 * Features:
 * - Primary answer display
 * - Collapsible sections with markdown content
 * - Source documents with PDF icons and tooltips
 * - Entity badges with category colors
 * - Ranked entity list with click-to-expand
 * - Follow-up question cards
 * - Metadata display for debugging
 */
const StructuredResponse = ({
  response,
  onNodeSelect,
  onSendMessage,
  showDebugInfo = true,
  legacySourceDocuments = [],
  legacySourceNodes = []
}) => {
  const [showAllEntities, setShowAllEntities] = useState(false);
  const [isExplanationExpanded, setIsExplanationExpanded] = useState(false);
  const [isSourcesExpanded, setIsSourcesExpanded] = useState(false);
  const [isEntitiesExpanded, setIsEntitiesExpanded] = useState(false);

  // Validate response structure
  const isValidResponse = useMemo(() => {
    return response && 
           response.version === '2.0' && 
           typeof response.answer === 'string' &&
           Array.isArray(response.sections) &&
           Array.isArray(response.sources) &&
           Array.isArray(response.entities) &&
           Array.isArray(response.badges) &&
           Array.isArray(response.follow_up);
  }, [response]);


  // Handle entity selection
  const handleEntitySelect = (entity) => {
    console.log('🔍 StructuredResponse handleEntitySelect - DEBUGGING ENTITY SELECTION:');
    console.log('1. Entity received from EntityList:', entity);
    console.log('2. onNodeSelect function exists:', typeof onNodeSelect === 'function');
    
    // Validate entity data before processing
    if (!entity) {
      console.error('❌ StructuredResponse: No entity provided to handleEntitySelect');
      return;
    }
    
    if (!entity.id) {
      console.error('❌ StructuredResponse: Entity missing required ID field:', entity);
      return;
    }
    
    if (onNodeSelect) {
      // Convert entity to node format for compatibility
      // Enhanced entity-to-node conversion with comprehensive field mapping
      const nodeData = {
        id: entity.id,
        // Ensure name field is populated with fallbacks
        name: entity.name || entity.label || entity.title || `Entity ${entity.id}`,
        label: entity.label || entity.name || `Entity ${entity.id}`,
        title: entity.title || entity.name || entity.label,
        description: entity.description || entity.summary || entity.content || 'No description available',
        category: entity.category || entity.type || 'Unknown',
        properties: entity.properties || {},
        relevance_score: entity.relevance_score || 0.5,
        // Add additional fields that might be expected by NodeDetails
        type: entity.type || entity.category || 'entity',
        summary: entity.summary || entity.description || 'No summary available',
        content: entity.content || entity.description || entity.summary || 'No content available',
        // Reference information for debugging
        source: 'entity_list'
      };
      
      // Validate the converted node data
      const isValidNodeData = !!(nodeData.id && nodeData.name);
      console.log('3. Entity validation - Valid entity ID:', !!entity.id);
      console.log('4. Entity validation - Valid entity name:', !!(entity.name || entity.label || entity.title));
      console.log('5. Node data validation - Valid node data:', isValidNodeData);
      console.log('6. Entity-to-node conversion - Original entity:', entity);
      console.log('7. Entity-to-node conversion - Converted nodeData:', nodeData);
      
      if (isValidNodeData) {
        console.log('8. ✅ Calling onNodeSelect with valid nodeData:', nodeData);
        onNodeSelect(nodeData);
        console.log('9. ✅ onNodeSelect call completed successfully');
      } else {
        console.error('❌ Invalid node data generated from entity:', { entity, nodeData });
      }
    } else {
      console.error('❌ StructuredResponse: onNodeSelect prop is missing or not a function');
    }
  };

  if (!isValidResponse) {
    return (
      <div className="structured-response error">
        <div className="error-message">
          <h4>Invalid Response Format</h4>
          <p>The response does not match the expected v2.0 structure.</p>
          {showDebugInfo && (
            <details>
              <summary>Debug Info</summary>
              <pre>{JSON.stringify(response, null, 2)}</pre>
            </details>
          )}
        </div>
      </div>
    );
  }

  const { answer, sections, sources, entities, follow_up, metadata } = response;



  // Handle hybrid response format - use legacy data when v2.0 arrays are empty
  const effectiveSources = useMemo(() => {
    if (sources && sources.length > 0) {
      return sources;
    }

    // Convert legacy sourceDocuments to v2.0 format
    if (legacySourceDocuments && legacySourceDocuments.length > 0) {
      const allSources = [];

      legacySourceDocuments.forEach((doc, docIndex) => {
        const content = doc.content || doc;

        // Check if this is a combined reference format like "[1] text [2] text [3] text"
        const referencePattern = /\[(\d+)\]\s*([^[]*?)(?=\[|\s*$)/g;
        const matches = [...content.matchAll(referencePattern)];

        if (matches.length > 0) {
          // Split combined references into individual sources
          matches.forEach(match => {
            const refNumber = parseInt(match[1], 10);
            const refContent = match[2].trim();

            allSources.push({
              id: `source-${refNumber}`,
              title: `Reference ${refNumber}`,
              content: refContent,
              type: 'document',
              url: doc.url || null,
              reference_number: refNumber
            });
          });
        } else {
          // Single reference format
          allSources.push({
            id: `source-${docIndex + 1}`,
            title: `Reference ${docIndex + 1}`,
            content: content,
            type: 'document',
            url: doc.url || null,
            reference_number: docIndex + 1
          });
        }
      });

      // Sort by reference number to maintain order
      return allSources.sort((a, b) => (a.reference_number || 0) - (b.reference_number || 0));
    }

    return [];
  }, [sources, legacySourceDocuments]);

  const effectiveEntities = useMemo(() => {
    if (entities && entities.length > 0) {
      return entities;
    }

    // Convert legacy sourceNodes to v2.0 format if needed
    if (legacySourceNodes && legacySourceNodes.length > 0) {
      return legacySourceNodes.map((node, index) => ({
        id: `entity-${index + 1}`,
        name: typeof node === 'string' ? node : node.name || `Entity ${index + 1}`,
        summary: typeof node === 'object' ? node.summary : null,
        relevance_score: typeof node === 'object' ? node.relevance_score : 0.5,
        type: 'entity'
      }));
    }

    return [];
  }, [entities, legacySourceNodes]);

  // Extract thinking content from answer
  const { thinkingContent, cleanAnswer } = useMemo(() => {
    if (!answer || typeof answer !== 'string') {
      return { thinkingContent: null, cleanAnswer: answer };
    }

    // Look for <think> tags in the answer
    const thinkingMatch = answer.match(/<think>([\s\S]*?)<\/think>/);
    
    if (thinkingMatch) {
      const thinking = thinkingMatch[1].trim();
      const answerWithoutThinking = answer.replace(/<think>[\s\S]*?<\/think>/, '').trim();
      return { thinkingContent: thinking, cleanAnswer: answerWithoutThinking };
    }

    return { thinkingContent: null, cleanAnswer: answer };
  }, [answer]);

  // Enhanced debug logging for entity data investigation
  if (process.env.NODE_ENV === 'development') {
    console.log('🔍 StructuredResponse ENTITY DATA INVESTIGATION:');
    console.log('1. Full response object:', response);
    console.log('2. Response version:', response?.version);
    console.log('3. Raw entities array:', entities);
    console.log('4. Entities length:', entities?.length);
    console.log('5. Entities type:', typeof entities);
    console.log('6. First entity (if exists):', entities?.[0]);
    console.log('7. Legacy source nodes:', legacySourceNodes);
    console.log('8. Legacy source nodes length:', legacySourceNodes?.length);
    console.log('9. Effective entities length:', effectiveEntities?.length);
    console.log('10. Effective entities array:', effectiveEntities);
    
    // Badges removed from UI; keeping logs minimal
    // Deep inspection of sections
    console.log('14. Sections array:', sections);
    console.log('15. Sections length:', sections?.length);
    
    // Log raw response structure
    console.log('16. Response Structure Keys:', Object.keys(response || {}));
    
    if (thinkingContent) {
      console.log('17. Thinking content extracted:', thinkingContent.substring(0, 100) + '...');
    }
    
    // Critical entity analysis
    if (effectiveEntities && effectiveEntities.length > 0) {
      console.log('✅ ENTITIES WILL BE DISPLAYED:', effectiveEntities);
      effectiveEntities.forEach((entity, index) => {
        console.log(`   Entity ${index}:`, {
          id: entity.id,
          name: entity.name,
          category: entity.category,
          description: entity.description,
          hasId: !!entity.id,
          hasName: !!entity.name,
          isValid: !!(entity.id && entity.name)
        });
      });
    } else {
      console.log('❌ NO ENTITIES TO DISPLAY - investigating why...');
      console.log('   - Raw entities from response:', entities);
      console.log('   - Legacy source nodes:', legacySourceNodes);
      console.log('   - Effective entities result:', effectiveEntities);
    }
  }

  // Limit entities shown initially
  const visibleEntities = showAllEntities ? effectiveEntities : effectiveEntities.slice(0, 5);

  return (
    <div className="structured-response">
      {/* Thinking Process Section */}
      {thinkingContent && (
        <ThinkingSection thinkingContent={thinkingContent} />
      )}

      {/* Primary Answer */}
      <div className="response-answer">
        <AnswerWithReferences
          content={cleanAnswer}
          sources={effectiveSources}
          entities={effectiveEntities}
          onSendMessage={onSendMessage}
          onNodeSelect={onNodeSelect}
        />
      </div>

      {/* Category Badges removed per requirement */}

      {/* Enhanced Explanation Section - Collapsible */}
      {sections.length > 0 && (
        <div className="response-explanation">
          <details open={isExplanationExpanded} onToggle={(e) => setIsExplanationExpanded(e.target.open)}>
            <summary 
              className="explanation-title"
              aria-expanded={isExplanationExpanded}
              aria-controls="explanation-content"
              role="button"
              tabIndex={0}
            >
              Explanation ({sections.filter(section => !section.title.toLowerCase().includes('follow up') && !section.title.toLowerCase().includes('follow-up')).length} sections)
            </summary>
            <div id="explanation-content" className="explanation-content-wrapper">
              {sections
                .filter(section => !section.title.toLowerCase().includes('follow up') && !section.title.toLowerCase().includes('follow-up'))
                .map((section, index) => (
                <div key={`explanation-${index}`} className="explanation-section">
                  <h5 className="explanation-section-title">{section.title}</h5>
                  <RichTextRenderer
                    content={section.content}
                    sectionType="explanation"
                    className="explanation-content"
                  />
                  {section.entity_refs && section.entity_refs.length > 0 && (
                    <div className="explanation-entities">
                      <span className="entity-refs-label">Related entities:</span>
                      {section.entity_refs.map(entityId => {
                        const entity = entities.find(e => e.id === entityId);
                        return entity ? (
                          <button
                            key={entityId}
                            className="entity-ref-button"
                            onClick={() => handleEntitySelect(entity)}
                            title={entity.description}
                          >
                            {entity.name}
                          </button>
                        ) : null;
                      })}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </details>
        </div>
      )}



      {/* Source Documents - Collapsible */}
      {sources.length > 0 && (
        <div className="response-sources">
          <details open={isSourcesExpanded} onToggle={(e) => setIsSourcesExpanded(e.target.open)}>
            <summary 
              className="sources-title"
              aria-expanded={isSourcesExpanded}
              aria-controls="sources-content"
              role="button"
              tabIndex={0}
            >
              Sources ({sources.length})
            </summary>
            <div id="sources-content" className="sources-grid">
              {sources.map((source, index) => (
                <SourceTooltip
                  key={source.id || `source-${index}`}
                  source={source}
                  index={index}
                />
              ))}
            </div>
          </details>
        </div>
      )}

      {/* Entity List - Collapsible */}
      {effectiveEntities.length > 0 && (
        <div className="response-entities">
          <details open={isEntitiesExpanded} onToggle={(e) => setIsEntitiesExpanded(e.target.open)}>
            <summary 
              className="entities-title"
              aria-expanded={isEntitiesExpanded}
              aria-controls="entities-content"
              role="button"
              tabIndex={0}
            >
              Related Entities ({effectiveEntities.length})
            </summary>
            <div id="entities-content" className="entities-content-wrapper">
              <EntityList
                entities={visibleEntities}
                onEntitySelect={handleEntitySelect}
                showRelevanceScore={true}
              />
              {effectiveEntities.length > 5 && (
                <button
                  className="show-more-entities"
                  onClick={() => setShowAllEntities(!showAllEntities)}
                >
                  {showAllEntities
                    ? `Show Less (${effectiveEntities.length - 5} hidden)`
                    : `Show All (${effectiveEntities.length - 5} more)`
                  }
                </button>
              )}
            </div>
          </details>
        </div>
      )}

      {/* Follow-up Questions (without heading) */}
      {follow_up.length > 0 && (
        <div className="response-followup">
          <FollowUpCards
            questions={follow_up}
            onQuestionClick={onSendMessage}
          />
        </div>
      )}

      {/* Enhanced Metadata */}
      {showDebugInfo && metadata && (
        <div className="response-metadata">
          <details>
            <summary>Response Metadata</summary>
            <div className="metadata-grid">
              {/* Only show processing time if it's meaningful (> 0) */}
              {metadata.processing_time_ms > 0 && (
                <div className="metadata-item">
                  <span className="metadata-label">Processing Time:</span>
                  <span className="metadata-value">{metadata.processing_time_ms}ms</span>
                </div>
              )}

              {/* Show search type */}
              {metadata.search_type && (
                <div className="metadata-item">
                  <span className="metadata-label">Search Type:</span>
                  <span className="metadata-value algorithm-name" title={metadata.search_type}>
                    {metadata.search_type
                      .replace('COMBINED_HYBRID_SEARCH_', '')
                      .replace('NODE_HYBRID_SEARCH_', '')
                      .replace(/_/g, ' ')
                      .toLowerCase()
                      .replace(/\b\w/g, l => l.toUpperCase())}
                  </span>
                </div>
              )}

              {/* Show entity and source counts */}
              <div className="metadata-item">
                <span className="metadata-label">Knowledge Graph Results:</span>
                <span className="metadata-value">
                  {effectiveSources.length} sources, {effectiveEntities.length} entities
                </span>
              </div>

              {/* Show confidence if available and meaningful */}
              {metadata.confidence_score > 0 && (
                <div className="metadata-item">
                  <span className="metadata-label">Confidence:</span>
                  <span className="metadata-value">
                    {(metadata.confidence_score * 100).toFixed(1)}%
                  </span>
                </div>
              )}

              {/* Show token usage if available and meaningful */}
              {metadata.token_usage > 0 && (
                <div className="metadata-item">
                  <span className="metadata-label">Token Usage:</span>
                  <span className="metadata-value">{metadata.token_usage}</span>
                </div>
              )}

              {/* Enhanced Graphiti Search Information */}
              {metadata.graphiti_search && (
                <div className="metadata-section">
                  <h4 className="metadata-section-title">Graphiti Search</h4>
                  <div className="metadata-item">
                    <span className="metadata-label">Algorithm:</span>
                    <span className="metadata-value algorithm-name" title={metadata.graphiti_search.algorithm}>
                      {metadata.graphiti_search.algorithm
                        .replace('COMBINED_HYBRID_SEARCH_', '')
                        .replace('NODE_HYBRID_SEARCH_', '')
                        .replace(/_/g, ' ')
                        .toLowerCase()
                        .replace(/\b\w/g, l => l.toUpperCase())}
                    </span>
                  </div>
                  {metadata.graphiti_search.diversity_factor !== undefined && (
                    <div className="metadata-item">
                      <span className="metadata-label">Diversity Factor:</span>
                      <span className="metadata-value">{metadata.graphiti_search.diversity_factor}</span>
                    </div>
                  )}
                  {metadata.graphiti_search.requested && (
                    <div className="metadata-item">
                      <span className="metadata-label">Requested:</span>
                      <span className="metadata-value">
                        {metadata.graphiti_search.requested.edges} edges, {metadata.graphiti_search.requested.nodes} nodes
                      </span>
                    </div>
                  )}
                  {metadata.graphiti_search.returned && (
                    <div className="metadata-item">
                      <span className="metadata-label">Returned:</span>
                      <span className="metadata-value">
                        {metadata.graphiti_search.returned.edges} edges, {metadata.graphiti_search.returned.nodes} nodes ({metadata.graphiti_search.returned.total_results} total)
                      </span>
                    </div>
                  )}
                </div>
              )}

              {/* Enhanced LLM Models Information */}
              {metadata.llm_models && (
                <div className="metadata-section">
                  <h4 className="metadata-section-title">LLM Models</h4>
                  
                  {metadata.llm_models.graphiti_llm && (
                    <div className="metadata-subsection">
                      <h5 className="metadata-subsection-title">Graphiti Search LLM:</h5>
                      <div className="metadata-item">
                        <span className="metadata-label">Model:</span>
                        <span className="metadata-value">{metadata.llm_models.graphiti_llm.model}</span>
                      </div>
                      {metadata.llm_models.graphiti_llm.purpose && (
                        <div className="metadata-item">
                          <span className="metadata-label">Purpose:</span>
                          <span className="metadata-value">{metadata.llm_models.graphiti_llm.purpose}</span>
                        </div>
                      )}
                    </div>
                  )}
                  
                  {metadata.llm_models.response_llm && (
                    <div className="metadata-subsection">
                      <h5 className="metadata-subsection-title">Response Generation LLM:</h5>
                      <div className="metadata-item">
                        <span className="metadata-label">Model:</span>
                        <span className="metadata-value">{metadata.llm_models.response_llm.model_used}</span>
                      </div>
                      {metadata.llm_models.response_llm.purpose && (
                        <div className="metadata-item">
                          <span className="metadata-label">Purpose:</span>
                          <span className="metadata-value">{metadata.llm_models.response_llm.purpose}</span>
                        </div>
                      )}
                      {metadata.llm_models.response_llm.request_parameters && (
                        <div className="metadata-item">
                          <span className="metadata-label">Temperature:</span>
                          <span className="metadata-value">{metadata.llm_models.response_llm.request_parameters.temperature}</span>
                        </div>
                      )}
                      {metadata.llm_models.response_llm.response_metrics && (
                        <div className="metadata-item">
                          <span className="metadata-label">Generation Time:</span>
                          <span className="metadata-value">{metadata.llm_models.response_llm.response_metrics.generation_time_ms}ms</span>
                        </div>
                      )}
                      {metadata.llm_models.response_llm.response_metrics && metadata.llm_models.response_llm.response_metrics.response_length_chars && (
                        <div className="metadata-item">
                          <span className="metadata-label">Response Length:</span>
                          <span className="metadata-value">
                            {metadata.llm_models.response_llm.response_metrics.response_length_chars} chars, {metadata.llm_models.response_llm.response_metrics.response_length_words} words
                          </span>
                        </div>
                      )}
                      {metadata.llm_models.response_llm.response_metrics && metadata.llm_models.response_llm.response_metrics.tokens_generated && (
                        <div className="metadata-item">
                          <span className="metadata-label">Tokens Generated:</span>
                          <span className="metadata-value">{metadata.llm_models.response_llm.response_metrics.tokens_generated}</span>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}

            </div>
          </details>
        </div>
      )}
    </div>
  );
};

export default StructuredResponse;
