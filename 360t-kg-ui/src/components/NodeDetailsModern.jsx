import React, { useState, useEffect, useCallback } from 'react';
import { getNodeDetails } from '../services/api';
import { getNodeIcon as getNodeIconFromMap } from '../constants/iconMap.js';
import pdfIcon from '../assets/logos/pdf_1979245.png';
import { getCategoryColor, getContrastTextColor } from '../constants/categoryColors';
import { formatTextWithNodeLinks, formatTextWithBullets } from '../utils/textFormatting.jsx';
import '../styles/NodeDetailsModern.css';

/**
 * Modern NodeDetails component with progressive disclosure and improved UX
 * @param {Object} selectedNode - The selected node object
 * @param {Function} onClose - Callback when closing details
 * @param {Function} onAnalysisResults - Callback when analysis results are received
 * @param {Function} onNodeSelect - Callback when a related node is selected
 * @param {Function} onNodeExpand - Callback when expanding a node
 * @param {Function} onRelationshipSelect - Callback when a relationship is selected
 * @param {Function} onCollapseChange - Callback when panel collapse state changes
 * @param {Function} onCenterNode - Callback to center the node in the graph view
 */
function NodeDetailsModern({ selectedNode, onClose, onAnalysisResults, onNodeSelect, onNodeExpand, onRelationshipSelect, onCollapseChange }) {
  const [nodeData, setNodeData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // State for progressive disclosure
  // Overview expanded by default
  const [expandedSections, setExpandedSections] = useState({
    properties: true,
    outgoing: false,
    incoming: false,
    documents: false
  });
  
  // State for expanded text fields
  const [expandedTexts, setExpandedTexts] = useState({
    summary: false,
    description: false,
    source_document: false
  });
  
  // State for resizable panel
  const [panelWidth, setPanelWidth] = useState(() => {
    const saved = localStorage.getItem('nodeDetailsWidth');
    return saved ? parseInt(saved) : 28;
  });
  const [isResizing, setIsResizing] = useState(false);
  
  // State for collapsible panel
  const [isCollapsed, setIsCollapsed] = useState(() => {
    const saved = localStorage.getItem('nodeDetailsCollapsed');
    return saved ? JSON.parse(saved) : false;
  });

  // Function to get appropriate icon for node type
  const getNodeIcon = useCallback((nodeType) => {
    return getNodeIconFromMap(nodeType);
  }, []);

  // Function to get node type from labels or fallback
  const getNodeType = useCallback((node) => {
    if (node.labels && Array.isArray(node.labels) && node.labels.length > 0) {
      return node.labels[0];
    }
    if (node.group) {
      return node.group;
    }
    return 'Unknown';
  }, []);

  // Enhanced function to get display name with comprehensive fallback logic
  const getDisplayName = useCallback((node) => {
    if (!node) return 'Unknown Node';
    
    // Try multiple property sources with enhanced fallback logic
    let name = node.properties?.name || 
               node.properties?.title ||
               node.properties?.label ||
               node.name || 
               node.label || 
               node.title ||
               node.id || 
               'Unknown Node';
    
    // Handle empty strings and null values
    if (!name || name.trim() === '' || name === 'null' || name === 'undefined') {
      // Try alternative property names
      name = node.properties?.displayName ||
             node.properties?.entityName ||
             node.properties?.nodeName ||
             node.summary?.substring(0, 50) ||
             node.description?.substring(0, 50) ||
             `Entity ${node.id}`;
    }
    
    // Clean up common prefixes/suffixes and normalize
    name = String(name).trim();
    name = name.replace(/^(show|display|view)\s+/i, '');
    name = name.replace(/\s+(node|entity|item)$/i, '');
    
    // Handle still empty after cleanup
    if (!name || name.trim() === '') {
      return `Entity ${node.id || 'Unknown'}`;
    }
    
    return name.trim();
  }, []);

  // Function to truncate text to specified lines (optimized for more content)
  const truncateToLines = useCallback((text, lines = 5) => {
    if (!text) return '';
    const words = text.split(' ');
    const wordsPerLine = 10; // Increased from 8 to show more content
    const maxWords = lines * wordsPerLine;
    
    if (words.length <= maxWords) return text;
    return words.slice(0, maxWords).join(' ');
  }, []);

  // Function to check if text needs truncation (optimized for more content)
  const needsTruncation = useCallback((text, lines = 5) => {
    if (!text) return false;
    const words = text.split(' ');
    const wordsPerLine = 10; // Increased from 8 to show more content
    const maxWords = lines * wordsPerLine;
    return words.length > maxWords;
  }, []);

  // Function to toggle text expansion
  const toggleTextExpansion = useCallback((field) => {
    setExpandedTexts(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  }, []);

  // Function to toggle section expansion
  const toggleSection = useCallback((section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  }, []);

  // Function to toggle panel collapse/expand
  const togglePanelCollapse = useCallback(() => {
    const newCollapsed = !isCollapsed;
    setIsCollapsed(newCollapsed);
    localStorage.setItem('nodeDetailsCollapsed', JSON.stringify(newCollapsed));
    
    // Notify parent component about the collapse state change
    if (onCollapseChange) {
      onCollapseChange(newCollapsed);
    }
  }, [isCollapsed, onCollapseChange]);


  // Fixed: Move useCallback to top level to avoid hooks violation
  const handleRelationshipNodeClick = useCallback((relatedNode, nodeDisplayName, relType) => {
    return (e) => {
      e.preventDefault();
      e.stopPropagation();

      console.log('🖱️ NodeDetailsModern: Relationship node clicked:', {
        nodeId: relatedNode?.id,
        nodeName: nodeDisplayName,
        relationshipType: relType
      });

      if (onNodeSelect && relatedNode) {
        try {
          onNodeSelect(relatedNode);
        } catch (error) {
          console.error('❌ Error in node selection:', error);
          setError('Failed to select related node. Please try again.');
        }
      } else {
        console.warn('⚠️ No node data or onNodeSelect handler available');
      }
    };
  }, [onNodeSelect, setError]);

  // Handler for clicking on the relationship itself (not the target node)
  const handleRelationshipClick = useCallback((rel, relatedNode) => {
    return (e) => {
      e.preventDefault();
      e.stopPropagation();

      console.log('🔗 NodeDetailsModern: Relationship clicked:', {
        relationshipType: rel?.type,
        relationshipDirection: rel?.direction,
        relatedNodeId: relatedNode?.id,
        relationshipProperties: rel?.properties
      });

      if (onRelationshipSelect && rel) {
        try {
          // Construct relationship data for the RelationshipDetails component
          const relationshipData = {
            id: `${nodeData.id}-${relatedNode?.id || 'unknown'}-${rel.type}`,
            type: rel.type,
            properties: rel.properties || {},
            fact: rel.properties?.fact || null,
            source: nodeData.id,
            target: relatedNode?.id || 'unknown',
            sourceNode: nodeData,
            targetNode: relatedNode,
            direction: rel.direction
          };

          onRelationshipSelect(relationshipData);
        } catch (error) {
          console.error('❌ Error in relationship selection:', error);
          setError('Failed to select relationship. Please try again.');
        }
      } else {
        console.warn('⚠️ No relationship data or onRelationshipSelect handler available');
      }
    };
  }, [onRelationshipSelect, nodeData, setError]);

  /**
   * Gets specific properties for nodes in the required order
   */
  const getSpecificProperties = useCallback((properties) => {
    if (!properties || typeof properties !== 'object') return {};

    const orderedProperties = {};
    const desiredProperties = ['summary', 'description', 'source_document', 'sourceDocument', 'document'];

    desiredProperties.forEach(key => {
      if (properties[key] && properties[key] !== null && properties[key] !== '') {
        orderedProperties[key] = properties[key];
      }
    });

    if (properties.url && properties.url !== null && properties.url !== '') {
      orderedProperties.url = properties.url;
    }

    return orderedProperties;
  }, []);

  // Load node details when selectedNode changes
  useEffect(() => {
    if (selectedNode && selectedNode.id) {
      console.log('🔍 NodeDetailsModern: Loading details for node:', selectedNode.id);
      console.log('🔍 NodeDetailsModern: Full selectedNode data:', selectedNode);
      console.log('🔍 NodeDetailsModern: selectedNode source:', selectedNode.source);
      console.log('🔍 NodeDetailsModern: selectedNode display name calculation:', getDisplayName(selectedNode));
      
      setIsLoading(true);
      setError(null);
      
      // If selectedNode is from chat reference, try to use it directly if it has complete data
      if (selectedNode.source === 'chat_reference' && selectedNode.name) {
        console.log('🎯 NodeDetailsModern: Using chat reference data directly');
        console.log('🎯 NodeDetailsModern: Chat entity has name:', selectedNode.name);
        
        // Set the node data directly from chat reference with enhanced structure
        const directNodeData = {
          id: selectedNode.id,
          name: selectedNode.name,
          label: selectedNode.label,
          title: selectedNode.title,
          type: selectedNode.type || selectedNode.category || 'entity',
          properties: {
            ...selectedNode.properties,
            name: selectedNode.name,
            description: selectedNode.description,
            category: selectedNode.category,
            summary: selectedNode.summary,
          },
          relationships: selectedNode.relationships || [],
          source: 'chat_reference_direct'
        };
        
        console.log('🎯 NodeDetailsModern: Direct node data structure:', directNodeData);
        setNodeData(directNodeData);
        setIsLoading(false);
        return;
      }
      
      getNodeDetails(selectedNode.id)
        .then((data) => {
          console.log('📊 NodeDetailsModern: Received node data from API:', data);
          console.log('🔗 NodeDetailsModern: Relationships count:', data?.relationships?.length || 0);
          
          if (data) {
            // If API data lacks name but selectedNode has it, merge them
            if (!data.name && selectedNode.name) {
              console.log('🔧 NodeDetailsModern: Merging selectedNode name with API data');
              data.name = selectedNode.name;
              data.properties = data.properties || {};
              data.properties.name = selectedNode.name;
            }
            
            setNodeData(data);
            
            // Log relationship details for debugging
            if (data.relationships && data.relationships.length > 0) {
              console.log('🔗 NodeDetailsModern: Relationship details:', data.relationships.map(rel => ({
                type: rel.type,
                direction: rel.direction,
                nodeId: rel.node?.id,
                nodeName: rel.node?.properties?.name || rel.node?.name
              })));
            }
          } else {
            setError('No data received for this node');
          }
        })
        .catch((err) => {
          console.error('❌ Error fetching node details:', err);
          console.log('🔄 NodeDetailsModern: API failed, trying to use selectedNode data directly');
          
          // If API fails but we have selectedNode data, use it directly
          if (selectedNode.name || selectedNode.label || selectedNode.title) {
            const fallbackNodeData = {
              id: selectedNode.id,
              name: selectedNode.name || selectedNode.label || selectedNode.title,
              label: selectedNode.label || selectedNode.name,
              title: selectedNode.title || selectedNode.name,
              type: selectedNode.type || selectedNode.category || 'entity',
              properties: {
                ...selectedNode.properties,
                name: selectedNode.name || selectedNode.label || selectedNode.title,
                description: selectedNode.description,
                category: selectedNode.category,
                summary: selectedNode.summary,
              },
              relationships: selectedNode.relationships || [],
              source: 'fallback_from_selected_node'
            };
            
            console.log('🔄 NodeDetailsModern: Using fallback node data:', fallbackNodeData);
            setNodeData(fallbackNodeData);
          } else {
            setError('Failed to load node details. Please try again.');
          }
        })
        .finally(() => {
          setIsLoading(false);
        });
    } else {
      console.log('⚠️ NodeDetailsModern: No selectedNode or selectedNode.id');
      console.log('⚠️ NodeDetailsModern: selectedNode:', selectedNode);
    }
  }, [selectedNode, getDisplayName]);



  // Handle panel resizing
  const handleMouseDown = useCallback((e) => {
    e.preventDefault();
    setIsResizing(true);
  }, []);

  const handleMouseMove = useCallback((e) => {
    if (!isResizing) return;
    
    const newWidth = ((window.innerWidth - e.clientX) / window.innerWidth) * 100;
    const clampedWidth = Math.max(20, Math.min(50, newWidth));
    setPanelWidth(clampedWidth);
  }, [isResizing]);

  const handleMouseUp = useCallback(() => {
    if (isResizing) {
      setIsResizing(false);
      localStorage.setItem('nodeDetailsWidth', panelWidth.toString());
    }
  }, [isResizing, panelWidth]);

  // Add mouse event listeners for resizing
  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'col-resize';
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = 'default';
      };
    }
  }, [isResizing, handleMouseMove, handleMouseUp]);

  // Function to split text into sentences intelligently
  const splitIntoSentences = useCallback((text) => {
    if (!text) return [];
    
    // Smart sentence splitting that handles common edge cases
    const sentences = text
      .replace(/([.!?])\s+/g, '$1|SPLIT|') // Mark sentence boundaries
      .split('|SPLIT|')
      .map(sentence => sentence.trim())
      .filter(sentence => sentence.length > 0)
      .filter(sentence => {
        // Filter out very short fragments that aren't meaningful sentences
        return sentence.length > 3 && /[a-zA-Z]/.test(sentence);
      });
    
    return sentences;
  }, []);

  // Render expandable text component with bullet points for sentences
  const renderExpandableText = (field, text, title) => {
    if (!text) return null;
    
    const sentences = splitIntoSentences(text);
    const isExpanded = expandedTexts[field];
    const maxSentences = (field === 'source_document') ? 3 : 4; // Show 3-4 sentences initially
    const needsExpansion = sentences.length > maxSentences;
    const displaySentences = isExpanded || !needsExpansion ? sentences : sentences.slice(0, maxSentences);

    // Handler for when user clicks on a related node link in text
    const handleNodeClick = (relatedNode) => {
      if (onNodeSelect && relatedNode) {
        try {
          console.log('🔗 NodeDetailsModern: Node clicked from text:', relatedNode.properties?.name);
          onNodeSelect(relatedNode);
        } catch (error) {
          console.error('❌ Error selecting node from text:', error);
          setError('Failed to select related node. Please try again.');
        }
      }
    };

    return (
      <div className="property-item" role="region" aria-label={title}>
        <div className="property-label">{title}</div>
        <div className={`text-content ${!isExpanded && needsExpansion ? 'truncated' : ''}`}>
          <ul className="sentence-bullet-list">
            {displaySentences.map((sentence, index) => (
              <li key={index} className="sentence-bullet-item">
                {formatTextWithNodeLinks(sentence, nodeData?.relationships || [], handleNodeClick)}
              </li>
            ))}
          </ul>
        </div>
        {needsExpansion && (
          <button
            className="show-more-button"
            onClick={() => toggleTextExpansion(field)}
            aria-expanded={isExpanded}
            aria-controls={`${field}-content`}
          >
            {isExpanded ? 'Collapse' : `Expand (${sentences.length - maxSentences} more)`}
          </button>
        )}
      </div>
    );
  };

  // Enhanced collapsible section component with better debugging and error handling
  const renderCollapsibleSection = (title, items, sectionKey, renderItem) => {
    console.log(`🔽 Rendering collapsible section: ${title}`, {
      itemCount: items?.length || 0,
      sectionKey,
      isExpanded: expandedSections[sectionKey]
    });

    if (!items || items.length === 0) {
      console.log(`⚠️ No items for section: ${title}`);
      return null;
    }
    
    const isExpanded = expandedSections[sectionKey];
    const previewCount = 4; // Show 4 items in preview
    const displayItems = isExpanded ? items : items.slice(0, previewCount);
    const hasMore = items.length > previewCount;

    return (
      <div className="section-card-modern collapsible-section">
        <div 
          className="section-header-card clickable"
          onClick={() => toggleSection(sectionKey)}
          title={`Click to ${isExpanded ? 'collapse' : 'expand'} ${title}`}
        >
          <div className="section-icon-title">
            <h4 className="section-title-card">
              {title}
            </h4>
          </div>
          <div className="section-controls">
            <span className="section-count">{items.length}</span>
            <span className={`expand-icon-card ${isExpanded ? 'expanded' : ''}`}>
              {isExpanded ? '−' : '+'}
            </span>
          </div>
        </div>
        {!isExpanded && hasMore && (
          <div className="preview-indicator-card">
            Showing {Math.min(previewCount, items.length)} of {items.length}
          </div>
        )}
        
        {(isExpanded || !hasMore || items.length <= previewCount) && (
          <div className="section-content">
            {displayItems.map((item, index) => {
              try {
                return renderItem(item, index);
              } catch (error) {
                console.error(`❌ Error rendering item ${index} in ${title}:`, error);
                return (
                  <div key={`error-${index}`} className="error-item">
                    <span>Error displaying relationship</span>
                  </div>
                );
              }
            })}
          </div>
        )}
        
        {!isExpanded && hasMore && (
          <div className="section-preview">
            {displayItems.map((item, index) => {
              try {
                return renderItem(item, index);
              } catch (error) {
                console.error(`❌ Error rendering preview item ${index} in ${title}:`, error);
                return (
                  <div key={`preview-error-${index}`} className="error-item">
                    <span>Error displaying relationship</span>
                  </div>
                );
              }
            })}
            <button 
              className="show-all-button-card"
              onClick={() => toggleSection(sectionKey)}
              title={`Show all ${items.length} items`}
            >
              <span>+ Show {items.length - previewCount} more</span>
            </button>
          </div>
        )}
      </div>
    );
  };

  // Enhanced relationship item rendering with better error handling and click functionality
  const renderRelationshipItem = (rel, index, keyPrefix) => {
    // Defensive programming - ensure we have valid relationship data
    if (!rel || typeof rel !== 'object') {
      console.warn('🔗 Invalid relationship data:', rel);
      return null;
    }

    const relType = rel.properties?.name || rel.type || 'Unknown';
    const relDirection = rel.direction || 'unknown';
    const relatedNode = rel.node;
    const nodeDisplayName = getDisplayName(relatedNode) || 'Unnamed Node';

    // Fixed: Use the top-level callback factory instead of useCallback inside render function
    const handleNodeClick = handleRelationshipNodeClick(relatedNode, nodeDisplayName, relType);
    const handleRelClick = handleRelationshipClick(rel, relatedNode);

    return (
      <div 
        key={`${keyPrefix}-${relType}-${relDirection}-${index}`} 
        className="relationship-item-modern"
        title={`${relType} → ${nodeDisplayName}`}
      >
        <div className="relationship-content">
          <div className="relationship-type-container">
            <span 
              className="relationship-type-compact"
              title={`Click to view relationship details: ${relType}`}
              onClick={handleRelClick}
              style={{ cursor: 'pointer' }}
            >
              {relType}
            </span>
          </div>
          <div className="relationship-direction-container">
            <span 
              className={`relationship-direction ${relDirection}`}
              title={`Click to view relationship details - ${relDirection === 'outgoing' ? 'Outgoing' : relDirection === 'incoming' ? 'Incoming' : 'Bidirectional'} relationship`}
              onClick={handleRelClick}
              style={{ cursor: 'pointer' }}
            >
              {relDirection === 'outgoing' ? (
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M5 12h14"></path>
                  <path d="M13 5l7 7-7 7"></path>
                </svg>
              ) : relDirection === 'incoming' ? (
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M19 12H5"></path>
                  <path d="M11 19l-7-7 7-7"></path>
                </svg>
              ) : (
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M5 12h14"></path>
                  <path d="M13 5l7 7-7 7"></path>
                  <path d="M11 19l-7-7 7-7"></path>
                </svg>
              )}
            </span>
          </div>
          <div 
            className="relationship-target"
            onClick={handleNodeClick}
            title={`Click to select node: ${nodeDisplayName}`}
            style={{ cursor: relatedNode ? 'pointer' : 'default' }}
          >
            <span 
              className="node-name-modern"
              title={nodeDisplayName}
            >
              {nodeDisplayName}
            </span>
            {(relatedNode?.category || relatedNode?.properties?.category) && (
              <span 
                className="category-badge-small"
                style={{
                  color: getContrastTextColor(getCategoryColor(relatedNode.category || relatedNode.properties?.category)),
                  backgroundColor: getCategoryColor(relatedNode.category || relatedNode.properties?.category),
                }}
                title={relatedNode.category || relatedNode.properties?.category}
              >
                {relatedNode.category || relatedNode.properties?.category}
              </span>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="details-panel-modern">
        <div className="loading-overlay">
          <div className="loading-spinner"></div>
          <p>Loading node details...</p>
        </div>
      </div>
    );
  }

  // Render error state if fetching fails
  if (error && !nodeData) {
    return (
      <div className="details-panel-modern">
        <div className="error-message" style={{ margin: '20px' }}>
          <p>⚠️ {error}</p>
          <button className="close-button" onClick={onClose} style={{ top: '10px', right: '10px' }}>×</button>
        </div>
      </div>
    );
  }

  // Render when no node is selected
  if (!nodeData) {
    return (
      <div className="details-panel-modern empty-panel">
        <p>No node selected</p>
      </div>
    );
  }

  const nodeType = nodeData.type;
  const iconSrc = `/svg/${getNodeIcon(nodeType)}`;
  const specificProperties = getSpecificProperties(nodeData.properties);
  
  // Debug logging for category extraction
  console.log('🏷️ NodeDetailsModern: Category Debug:', {
    nodeId: nodeData.id,
    nodeName: nodeData.name,
    nodeCategory: nodeData.category,
    propertiesCategory: nodeData.properties?.category,
    nodeType: nodeType,
    allProperties: nodeData.properties,
    fullNodeData: nodeData
  });

  // Separate relationships by type with enhanced debugging
  const allRelationships = nodeData.relationships || [];
  console.log('🔗 NodeDetailsModern: Processing relationships:', {
    total: allRelationships.length,
    relationships: allRelationships.map(rel => ({
      type: rel?.type,
      direction: rel?.direction,
      hasNode: !!rel?.node,
      nodeId: rel?.node?.id
    }))
  });

  const mentionsRelationships = allRelationships.filter(rel => 
    rel && typeof rel === 'object' && rel.type === 'MENTIONS'
  );
  const otherRelationships = allRelationships.filter(rel => 
    rel && typeof rel === 'object' && rel.type !== 'MENTIONS'
  );
  const outgoingRelationships = otherRelationships.filter(rel => rel.direction === 'outgoing');
  const incomingRelationships = otherRelationships.filter(rel => rel.direction === 'incoming');

  console.log('🔗 NodeDetailsModern: Relationship categories:', {
    mentions: mentionsRelationships.length,
    outgoing: outgoingRelationships.length,
    incoming: incomingRelationships.length,
    total: allRelationships.length
  });

  return (
    <div 
      className={`node-details-modern ${isCollapsed ? 'collapsed' : ''}`} 
      style={{ '--panel-width': isCollapsed ? '48px' : `${panelWidth}%` }}
    >
      {/* Resize Handle */}
      {!isCollapsed && (
        <div 
          className={`resize-handle ${isResizing ? 'resizing' : ''}`}
          onMouseDown={handleMouseDown}
          title="Drag to resize panel"
        />
      )}
      
      {/* Header Section - redesigned for clarity and reduced noise */}
      <div className={`node-details-header node-details-header--modern ${isCollapsed ? 'collapsed' : ''}`}>
        <div className={`node-header-left ${isCollapsed ? 'collapsed' : ''}`}>
          <div className="node-title-wrap">
            <h2
              className="node-title node-title--singleline"
              title={nodeData.properties?.name || nodeData.name || 'Unknown Node'}
            >
              {nodeData.properties?.name || nodeData.name || 'Unknown Node'}
            </h2>
          </div>
        </div>

        <div className="node-meta">
          <div className="chips" role="list">
            {(nodeData.category || nodeData.properties?.category) && (
              <span
                className="relation-badge relation-badge--header"
                role="listitem"
                title={nodeData.category || nodeData.properties?.category}
                style={{
                  color: getContrastTextColor(
                    getCategoryColor(nodeData.category || nodeData.properties?.category)
                  ),
                  backgroundColor: getCategoryColor(
                    nodeData.category || nodeData.properties?.category
                  ),
                  borderColor: getCategoryColor(
                    nodeData.category || nodeData.properties?.category
                  ),
                }}
              >
                <span className="relation-badge__text">
                  {nodeData.category || nodeData.properties?.category}
                </span>
              </span>
            )}

            {nodeType && nodeType !== 'Unknown' && (
              <span className="chip chip--type" role="listitem" title={`Type: ${nodeType}`}>
                <span className="chip-value">{nodeType}</span>
              </span>
            )}
          </div>
        </div>

        <div className="node-actions">
          <button
            className="icon-btn"
            onClick={togglePanelCollapse}
            title={isCollapsed ? 'Expand details panel' : 'Collapse details panel'}
            aria-label={isCollapsed ? 'Expand panel' : 'Collapse panel'}
          >
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              {/* Requirement: Expand should point left, Collapse should point right */}
              {/* When collapsed, we show a LEFT-pointing chevron to indicate expand-to-the-left */}
              {/* When expanded, we show a RIGHT-pointing chevron to indicate collapse-to-the-right */}
              <path d={isCollapsed ? 'M15 18l-6-6 6-6' : 'M9 18l6-6-6-6'} />
            </svg>
          </button>
          <button
            className="icon-btn"
            onClick={onClose}
            title="Close details panel"
            aria-label="Close panel"
          >
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M18 6L6 18M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      {!isCollapsed && (
                <div className="node-details-content">
          {/* Overview (Summary / Description / Source) */}
          {Object.keys(specificProperties || {}).length > 0 && (
            <div className="section-card-modern collapsible-section">
              <div
                className="section-header-card clickable"
                role="button"
                aria-expanded={expandedSections.properties}
                aria-controls="overview-section"
                onClick={() => toggleSection('properties')}
                title={`Click to ${expandedSections.properties ? 'collapse' : 'expand'} Overview`}
              >
                <div className="section-icon-title">
                  <h3 className="section-title-card">Overview</h3>
                </div>
                <div className="section-controls">
                  <span className="section-count">
                    {['summary','description','source_document','sourceDocument','document','url'].filter(k => specificProperties[k]).length}
                  </span>
                  <span className={`expand-icon-card ${expandedSections.properties ? 'expanded' : ''}`} aria-hidden="true">
                    {expandedSections.properties ? '−' : '+'}
                  </span>
                </div>
              </div>

              <div id="overview-section" className="content-section" style={{ display: expandedSections.properties ? 'block' : 'none' }}>
                <div className="property-grid">
                  {specificProperties.summary && renderExpandableText('summary', specificProperties.summary, 'Summary')}
                  {specificProperties.description && renderExpandableText('description', specificProperties.description, 'Description')}
                  {(specificProperties.source_document || specificProperties.sourceDocument || specificProperties.document) &&
                    renderExpandableText(
                      'source_document',
                      specificProperties.source_document || specificProperties.sourceDocument || specificProperties.document,
                      'Source Document'
                    )}
                  {specificProperties.url && (
                    <div className="property-item">
                      <div className="property-label">Source Document</div>
                      <a
                        href={specificProperties.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="property-value"
                        style={{ color: 'var(--360t-primary)', textDecoration: 'underline' }}
                        title="Open source document"
                      >
                        View Source
                      </a>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

        {/* Enhanced Labels Section with Card Layout */}
        {nodeData.labels && nodeData.labels.length > 0 && (
          <div className="section-card-modern labels-section">
            <div className="section-header-card">
              <div className="section-icon-title">
                <span className="section-icon" aria-hidden="true">
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.6" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M3 7a2 2 0 0 1 2-2h5l9 9a2 2 0 0 1 0 3l-4 4a2 2 0 0 1-3 0L3 12V7z"></path>
                    <circle cx="7.5" cy="7.5" r="1.5"></circle>
                  </svg>
                </span>
                <h3 className="section-title-card">Labels</h3>
              </div>
              <span className="section-count">{nodeData.labels.length}</span>
            </div>
            <div className="labels-container-card">
              {nodeData.labels.map((label, index) => (
                <span key={index} className="label-badge-card" title={`Label: ${label}`}>
                  {label}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Enhanced Relationships Sections with Card Layout */}
        {outgoingRelationships.length > 0 && renderCollapsibleSection(
          'Outgoing Relationships',
          outgoingRelationships,
          'outgoing',
          (rel, index) => renderRelationshipItem(rel, index, 'outgoing')
        )}

        {incomingRelationships.length > 0 && renderCollapsibleSection(
          'Incoming Relationships',
          incomingRelationships,
          'incoming',
          (rel, index) => renderRelationshipItem(rel, index, 'incoming')
        )}

        {mentionsRelationships.length > 0 && renderCollapsibleSection(
          'Document References',
          mentionsRelationships,
          'documents',
          (rel, index) => renderRelationshipItem(rel, index, 'mentions')
        )}

        {/* Error Section */}
        {error && (
          <div className="error-message-modern">
            <p>⚠️ {error}</p>
          </div>
        )}
        </div>
      )}
    </div>
  );
}

export default NodeDetailsModern;
